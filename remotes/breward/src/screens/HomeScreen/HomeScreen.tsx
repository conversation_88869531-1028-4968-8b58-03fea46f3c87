import React from 'react';
import Animated from 'react-native-reanimated';
import {
  BlockView,
  ConditionView,
  DeviceHelper,
  IconImage,
  RewardGroupType,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import EmptyRewards from '@src/components/EmptyRewards';
import { useScroll } from '@src/hooks';

import { icSearch } from '@images';

import CarouselReward from './components/CarouselReward';
import FlatListReward from './components/FlatListReward';
import Header from './components/Header';
import HotDeals from './components/HotDeals';
import MenuNavigator from './components/MenuNavigator';
import PrivilegesReward from './components/PrivilegesReward';
import SkeletonListReward from './components/SkeletonListReward';
import YourPrivileges from './components/YourPrivileges';
import useHomeScreen from './hook';

export const HomeScreen: React.FC = () => {
  const {
    listReward,
    isFetching,
    onSeeMore,
    user,
    t,
    onPressVoucher,
    onPressSearch,
    numberRewardOfUser,
  } = useHomeScreen();
  const { scrollY, onScroll } = useScroll();

  return (
    <BlockView flex={1}>
      <Header
        scrollY={scrollY}
        numberRewardOfUser={numberRewardOfUser}
      />

      <Animated.ScrollView
        showsVerticalScrollIndicator={false}
        onScroll={onScroll}
      >
        <MenuNavigator />

        <BlockView height={24} />

        {isFetching && <SkeletonListReward />}

        <BlockView
          gap={Spacing.SPACE_40}
          inset={'bottom'}
        >
          <ConditionView
            condition={!isFetching && listReward.length === 0}
            viewTrue={
              <EmptyRewards
                title={t('VOUCHER_NOT_YET')}
                description={t('VOUCHER_NOT_YET_DESCRIPTION')}
              />
            }
            viewFalse={
              <>
                {listReward
                  .sort((a, b) => (a.Weight ?? 0) - (b.Weight ?? 0))
                  .map((item, index) => {
                    switch (item.type) {
                      case RewardGroupType.RECOMMEND_FOR_YOU:
                        return (
                          <CarouselReward
                            onPressVoucher={onPressVoucher}
                            key={index}
                            {...item}
                          />
                        );
                      case RewardGroupType.EXCLUSIVE_DEAL:
                        return (
                          <FlatListReward
                            onPressVoucher={onPressVoucher}
                            key={index}
                            {...item}
                            onSeeMore={onSeeMore}
                          />
                        );
                      case RewardGroupType.TOP_DEAL:
                        return (
                          <PrivilegesReward
                            onPressVoucher={onPressVoucher}
                            onSeeMore={onSeeMore}
                            key={index}
                            {...item}
                          />
                        );
                      case RewardGroupType.FLASH_SALE:
                        return (
                          <HotDeals
                            onPressVoucher={onPressVoucher}
                            key={index}
                            {...item}
                          />
                        );
                      default:
                        return null;
                    }
                  })}
              </>
            }
          />
          {user && <YourPrivileges />}
        </BlockView>
      </Animated.ScrollView>

      <TouchableOpacity
        absolute
        positionRight={24}
        positionBottom={24}
        inset={'bottom'}
        onPress={onPressSearch}
      >
        <IconImage
          source={icSearch}
          size={60 * DeviceHelper.WIDTH_RATIO}
        />
      </TouchableOpacity>
    </BlockView>
  );
};
