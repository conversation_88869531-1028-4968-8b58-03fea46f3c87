import { StyleSheet } from 'react-native';
import { BorderRadius, <PERSON>ceHelper, Spacing } from '@btaskee/design-system';

const { WIDTH } = DeviceHelper.WINDOW;

const WIDTH_IMAGE = WIDTH * 0.75;
const HEIGHT_IMAGE = WIDTH_IMAGE * 0.5;
export default StyleSheet.create({
  boxContainer: {
    flex: 1,
  },
  container: {
    flexDirection: 'row',
    marginTop: Spacing.SPACE_20,
  },
  containerItemRight: {
    padding: Spacing.SPACE_20,
    width: '80%',
  },
  containerItem: {
    paddingVertical: Spacing.SPACE_20,
    marginLeft: Spacing.SPACE_20,
    width: '70%',
  },
  image: {
    height: HEIGHT_IMAGE,
    borderRadius: BorderRadius.RADIUS_04,
    width: '100%',
  },
  leftItemContainer: {
    marginTop: Spacing.SPACE_16,
    height: '100%',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  boxPlaceHolder1: {
    marginTop: Spacing.SPACE_12,
    width: '100%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },
  boxHeader: {
    marginBottom: Spacing.SPACE_12,
    width: '70%',
    height: 25,
    borderRadius: BorderRadius.RADIUS_04,
  },
  boxPlaceHolder2: {
    marginTop: Spacing.SPACE_12,
    width: '50%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },
  boxPlaceHolder3: {
    width: '30%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },
  boxPlaceHolder4: {
    width: '30%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },
  boxBottom: {
    marginTop: Spacing.SPACE_16,
    justifyContent: 'space-between',
  },
});
