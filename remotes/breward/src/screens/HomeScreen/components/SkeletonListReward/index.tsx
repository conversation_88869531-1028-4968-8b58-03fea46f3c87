import React, { memo } from 'react';
import { BlockView, SkeletonBox } from '@btaskee/design-system';

import styles from './styles';

const SkeletonListReward = () => {
  const listReward = [1, 2];

  return (
    <BlockView style={styles.boxContainer}>
      {listReward.map((item) => (
        <BlockView
          key={item}
          style={styles.container}
        >
          <BlockView style={styles.containerItem}>
            <SkeletonBox style={styles.boxHeader} />
            <SkeletonBox style={styles.image} />
            <SkeletonBox style={styles.boxPlaceHolder1} />
            <SkeletonBox style={styles.boxPlaceHolder2} />

            <BlockView
              flex
              style={styles.leftItemContainer}
            >
              <SkeletonBox style={styles.boxPlaceHolder3} />
              <SkeletonBox style={styles.boxPlaceHolder4} />
            </BlockView>
          </BlockView>
          <BlockView style={styles.containerItem}>
            <SkeletonBox style={styles.boxHeader} />
            <SkeletonBox style={styles.image} />
            <SkeletonBox style={styles.boxPlaceHolder1} />
            <SkeletonBox style={styles.boxPlaceHolder2} />

            <BlockView
              flex
              style={styles.leftItemContainer}
            >
              <SkeletonBox style={styles.boxPlaceHolder3} />
              <SkeletonBox style={styles.boxPlaceHolder4} />
            </BlockView>
          </BlockView>
        </BlockView>
      ))}
    </BlockView>
  );
};

export default memo(SkeletonListReward);
