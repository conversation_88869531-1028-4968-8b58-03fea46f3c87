import { useCallback } from 'react';
import {
  EndpointKeys,
  RewardItem,
  useApiQuery,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';
import { RouteName } from '@src/navigation/RouteName';

const useHomeScreen = () => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();

  const { user } = useUserStore();

  const onSeeMore = useCallback(
    (type: string, title: string, endDate?: string) => {
      // navigation.navigate(RouteName.AllRewards, {
      //   type,
      //   title,
      //   saleEndDate: endDate,
      // });
    },
    [],
  );

  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getRewardHomePage,
    params: {
      isoCode,
    },
  });

  const onPressVoucher = useCallback(
    (item: RewardItem) => {
      navigation.navigate(RouteName.RewardDetail, { rewardId: item._id });
    },
    [navigation],
  );

  const onPressSearch = useCallback(() => {
    navigation.navigate(RouteName.Search);
  }, [navigation]);

  return {
    t,
    i18n,
    user,
    navigation,
    isFetching,
    listReward: data?.listReward || [],
    numberRewardOfUser: data?.numberRewardOfUser || 0,
    onSeeMore,
    onPressVoucher,
    onPressSearch,
  };
};

export default useHomeScreen;
