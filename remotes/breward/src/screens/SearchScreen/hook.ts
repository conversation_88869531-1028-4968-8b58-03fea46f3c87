import { useCallback, useMemo, useState } from 'react';
import {
  EndpointKeys,
  useApiInfiniteQuery,
  useAppStore,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';
import { flatten, isArray, isNil, size } from 'lodash-es';

const DEFAULT_LIMIT = 10;

const useSearchScreen = () => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const [searchText, setSearchText] = useState('');

  const { data, fetchNextPage, isFetching, hasNextPage, isFetchingNextPage } =
    useApiInfiniteQuery({
      key: EndpointKeys.getListReward,
      params: {
        isoCode,
        limit: DEFAULT_LIMIT,
        filterBy: {
          searchText,
        },
        sortBy: 'LATEST',
      },
      options: {
        getNextPageParam: (lastPage, allPages) => {
          if (isArray(lastPage) && size(lastPage) < DEFAULT_LIMIT) {
            return undefined;
          }
          return allPages.length + 1;
        },
        enabled: !!searchText,
      },
    });

  const flattenedData = useMemo(() => {
    return flatten(data?.pages).filter((item) => !isNil(item));
  }, [data]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, isFetching, fetchNextPage]);

  const onSearchText = useCallback((text: string) => {
    setSearchText(text);
  }, []);

  return {
    navigation,
    t,
    i18n,
    onSearchText,
    isFetching,
    flattenedData,
    handleEndReached,
  };
};

export default useSearchScreen;
