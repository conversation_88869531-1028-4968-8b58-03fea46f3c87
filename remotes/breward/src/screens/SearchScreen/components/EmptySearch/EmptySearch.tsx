import React, { memo } from 'react';
import {
  BlockView,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FastImage,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { searchVoucher } from '@images';

const EmptySearch = () => {
  const { t } = useI18n();

  return (
    <BlockView
      gap={Spacing.SPACE_24}
      center
      padding={{ horizontal: Spacing.SPACE_12, vertical: 200 }}
    >
      <FastImage
        source={searchVoucher}
        style={{
          width: 140 * DeviceHelper.WIDTH_RATIO,
          height: 140 * DeviceHelper.WIDTH_RATIO,
        }}
      />
      <CText
        center
        size={FontSizes.SIZE_14}
        lineHeight={Spacing.SPACE_20}
      >
        {t('SEARCH_OFFERS_PLACEHOLDER')}
      </CText>
    </BlockView>
  );
};

export default memo(EmptySearch);
