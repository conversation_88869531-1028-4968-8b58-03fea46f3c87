import React, { memo } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  CTextInput,
  FontSizes,
  IconAssets,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import useSearchBar, { SearchBarProps } from './hook';
import styles from './styles';

const SearchBar = ({ onSearchText }: SearchBarProps) => {
  const { t, navigation, handleChangeText } = useSearchBar({ onSearchText });

  return (
    <BlockView
      row
      horizontal
      gap={Spacing.SPACE_12}
      padding={{ horizontal: Spacing.SPACE_16, vertical: Spacing.SPACE_08 }}
      border={{
        bottom: {
          width: 1,
          color: ColorsV2.neutral50,
        },
      }}
    >
      <CTextInput
        leftIcon={
          <IconImage
            source={IconAssets.icSearch}
            size={16}
            style={styles.icon}
          />
        }
        inputStyle={styles.inputStyle}
        inputContainerStyle={styles.inputContainerStyle}
        placeholder={t('SEARCH_OFFERS')}
        containerStyle={styles.containerStyle}
        onChangeText={handleChangeText}
        onSubmitEditing={(e) => {
          const text = e.nativeEvent.text;
          if (text) {
            onSearchText(text);
          }
        }}
      />
      <TouchableOpacity onPress={navigation.goBack}>
        <CText
          size={FontSizes.SIZE_16}
          color={ColorsV2.neutral800}
          semiBold
        >
          {t('CANCEL')}
        </CText>
      </TouchableOpacity>
    </BlockView>
  );
};

export default memo(SearchBar);
