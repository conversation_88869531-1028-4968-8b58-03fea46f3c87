import { useCallback } from 'react';
import { useAppNavigation, useI18n } from '@src/hooks';
import debounce from 'lodash-es/debounce';

export interface SearchBarProps {
  onSearchText: (text: string) => void;
}

const useSearchBar = ({ onSearchText }: SearchBarProps) => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  const handleChangeWithDebounce = useCallback(
    debounce((text) => {
      onSearchText(text);
    }, 1200),
    [],
  );

  const handleChangeText = useCallback(
    (text: string) => {
      handleChangeWithDebounce(text);
    },
    [handleChangeWithDebounce],
  );

  return { navigation, t, i18n, handleChangeText };
};

export default useSearchBar;
