import React, { memo } from 'react';
import { BlockView, ScrollView } from '@btaskee/design-system';

import EmptySearch from './components/EmptySearch';
import SearchBar from './components/SearchBar';
import useSearchScreen from './hook';
import styles from './styles';

export const SearchScreen: React.FC = memo(() => {
  const {
    t,
    i18n,
    navigation,
    onSearchText,
    flattenedData,
    handleEndReached,
    isFetching,
  } = useSearchScreen();

  return (
    <BlockView inset={'top'}>
      <SearchBar onSearchText={onSearchText} />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <EmptySearch />
      </ScrollView>
    </BlockView>
  );
});
