import React from 'react';
import { BlockView } from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import useExclusiveBrandDetailScreen, {
  MyRewardDetailScreenProps,
} from './hook';

export const ExclusiveBrandDetailScreen: React.FC<MyRewardDetailScreenProps> = (
  props,
) => {
  const { t, i18n, data, isFetching } = useExclusiveBrandDetailScreen(props);

  if (isEmpty(data)) return null;

  return <BlockView flex={1} />;
};
