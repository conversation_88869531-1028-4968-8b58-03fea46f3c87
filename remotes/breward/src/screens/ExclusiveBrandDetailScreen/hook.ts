import { EndpointKeys, useApiQuery, useAppStore } from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { RouteName } from '@src/navigation/RouteName';
import { RootStackScreenProps } from '@src/navigation/types';

export type MyRewardDetailScreenProps = RootStackScreenProps<
  typeof RouteName.ExclusiveBrandDetail
>;

const useExclusiveBrandDetailScreen = ({
  navigation,
  route,
}: MyRewardDetailScreenProps) => {
  const { t, i18n } = useI18n();
  const { isoCode } = useAppStore();

  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getMarketingCampaignDetail,
    params: {
      campaignId: route.params.brandId,
      isoCode: isoCode,
    },
  });

  return { navigation, t, i18n, data, isFetching };
};

export default useExclusiveBrandDetailScreen;
