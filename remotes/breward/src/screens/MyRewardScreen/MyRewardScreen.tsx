import React, { useCallback, useMemo } from 'react';
import { BlockView, ColorsV2, CText, FontSizes } from '@btaskee/design-system';

import MyNewRewardTab from './components/MyNewRewardTab';
import MyUsedRewardTab from './components/MyUsedRewardTab';
import useMyRewardScreen from './hook';

export const MyRewardScreen: React.FC = () => {
  const { t, Tab } = useMyRewardScreen();

  const LIST_TABS = useMemo(
    () => [
      { label: 'REWARD_TAB_NEW', Component: MyNewRewardTab },
      { label: 'REWARD_TAB_USED', Component: MyUsedRewardTab },
    ],
    [],
  );

  const renderTabBar = useCallback(
    ({
      children,
      color,
      focused,
    }: {
      focused: boolean;
      color: string;
      children: string;
    }) => {
      return (
        <CText
          size={FontSizes.SIZE_14}
          color={focused ? ColorsV2.orange500 : color}
          bold={focused}
        >
          {t(children)}
        </CText>
      );
    },
    [t],
  );

  return (
    <BlockView
      flex={1}
      backgroundColor={ColorsV2.neutralWhite}
    >
      <Tab.Navigator
        screenOptions={{
          lazy: true,
          tabBarAllowFontScaling: false,
          tabBarActiveTintColor: ColorsV2.orange500,
          tabBarInactiveTintColor: ColorsV2.neutral500,
          tabBarIndicatorStyle: {
            backgroundColor: ColorsV2.orange500,
          },
          tabBarLabelStyle: {
            fontSize: FontSizes.SIZE_14,
          },
          tabBarLabel: renderTabBar,
        }}
      >
        {LIST_TABS.map(({ Component, label }, index) => {
          return (
            <Tab.Screen
              key={index}
              name={label}
            >
              {() => <Component />}
            </Tab.Screen>
          );
        })}
      </Tab.Navigator>
    </BlockView>
  );
};
