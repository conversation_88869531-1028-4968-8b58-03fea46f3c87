import React, { memo } from 'react';
import { BlockView, SkeletonBox } from '@btaskee/design-system';

import { styles } from './styles';

/**
 * Individual skeleton item component for reward list
 * Purpose: Displays a single skeleton placeholder while reward data is loading
 * @returns {JSX.Element} Skeleton item with image and text placeholders
 */
const SkeletonItem = () => {
  return (
    <BlockView
      row
      style={styles.container}
    >
      <SkeletonBox style={styles.imageBox} />
      <BlockView
        flex
        style={styles.contentContainer}
      >
        <SkeletonBox style={styles.textBox1} />
        <SkeletonBox style={styles.textBox2} />
        <SkeletonBox style={styles.textBox3} />
      </BlockView>
    </BlockView>
  );
};

/**
 * Skeleton loading component for My Reward screen
 * Purpose: Shows multiple skeleton items while reward list is loading
 * @returns {JSX.Element} List of skeleton items
 */
const SkeletonMyReward = () => {
  const QUANTITY_SKELETON = 5;
  const LIST_SKELETON = Array.from({ length: QUANTITY_SKELETON }, (_, i) => i);

  return (
    <BlockView>
      {LIST_SKELETON.map((index) => (
        <SkeletonItem key={index} />
      ))}
    </BlockView>
  );
};

export default memo(SkeletonMyReward);
