import React, { memo, useCallback } from 'react';
import { RefreshControl } from 'react-native';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  FlatList,
  PromotionItem,
  Spacing,
} from '@btaskee/design-system';
import EmptyRewards from '@src/components/EmptyRewards';
import LoadingFooter from '@src/components/LoadingFooter';
import VoucherItem from '@src/components/VoucherItem';

import SkeletonMyReward from '../SkeletonMyReward';
import useMyUsedRewardTab from './hook';
import styles from './styles';

const MyUsedRewardTab = () => {
  const {
    t,
    data,
    isFetching,
    isRefetching,
    isFetchingNextPage,
    onRefresh,
    handleEndReached,
  } = useMyUsedRewardTab();

  const listEmptyComponent = useCallback(() => {
    return (
      <ConditionView
        condition={isFetching}
        viewTrue={<SkeletonMyReward />}
        viewFalse={
          <EmptyRewards
            title={t('REWARD_TAB_EMPTY')}
            description={t('REWARD_TAB_EMPTY_DESCRIPTION')}
          />
        }
      />
    );
  }, [isFetching, t]);

  const renderItem = useCallback(({ item }: { item: PromotionItem }) => {
    return (
      <VoucherItem
        {...item}
        containerStyle={styles.itemContainer}
        dashColor={ColorsV2.neutralWhite}
      />
    );
  }, []);

  return (
    <BlockView
      flex={1}
      backgroundColor={ColorsV2.neutralWhite}
    >
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={(item, index) => index.toString()}
        ListEmptyComponent={listEmptyComponent}
        ListFooterComponent={<LoadingFooter isVisible={isFetchingNextPage} />}
        contentContainerStyle={styles.contentContainerStyle}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={onRefresh}
            colors={[ColorsV2.orange50, ColorsV2.orange500]} // Android
            tintColor={ColorsV2.orange500} // iOS
            progressBackgroundColor={ColorsV2.neutralSecondary} // Android
            progressViewOffset={Spacing.SPACE_04}
          />
        }
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.8}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        windowSize={10}
        initialNumToRender={10}
        updateCellsBatchingPeriod={100}
      />
    </BlockView>
  );
};

export default memo(MyUsedRewardTab);
