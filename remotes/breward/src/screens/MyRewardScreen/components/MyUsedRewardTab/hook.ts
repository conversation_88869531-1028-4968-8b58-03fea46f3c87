import { useCallback, useMemo } from 'react';
import {
  EndpointKeys,
  useApiInfiniteQuery,
  useAppStore,
  useQueryClient,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';
import { flatten, isArray, isNil, size } from 'lodash-es';

const DEFAULT_LIMIT = 10;

const useMyUsedRewardTab = () => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const queryClient = useQueryClient();

  const {
    data,
    fetchNextPage,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    isRefetching,
    refetch,
  } = useApiInfiniteQuery({
    key: EndpointKeys.getMyRewards,
    params: {
      isoCode,
      limit: DEFAULT_LIMIT,
      data: { isUsed: true },
    },
    options: {
      getNextPageParam: (lastPage, allPages) => {
        if (isArray(lastPage) && size(lastPage) < DEFAULT_LIMIT) {
          return undefined;
        }
        return allPages.length + 1;
      },
    },
  });

  const flattenedData = useMemo(() => {
    return flatten(data?.pages).filter((item) => !isNil(item));
  }, [data]);

  const onRefresh = useCallback(() => {
    queryClient.removeQueries({ queryKey: [EndpointKeys.getMyRewards] });
    refetch().catch();
  }, [queryClient, refetch]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isFetching) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, isFetching, fetchNextPage]);

  return {
    navigation,
    t,
    i18n,
    data: flattenedData,
    isFetching,
    isRefetching,
    isFetchingNextPage,
    onRefresh,
    handleEndReached,
  };
};

export default useMyUsedRewardTab;
