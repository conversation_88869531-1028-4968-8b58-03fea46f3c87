import { useCallback } from 'react';
import { ToastHelpers } from '@btaskee/design-system';
import Clipboard from '@react-native-clipboard/clipboard';
import { useAppNavigation, useI18n } from '@src/hooks';

const useQrCodeReward = (code: string) => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  const handleCopy = useCallback(() => {
    Clipboard.setString(code);
    ToastHelpers.showSuccess({
      message: t('COPIED'),
      position: 'top',
    });
  }, [code, t]);

  return { navigation, t, i18n, handleCopy };
};

export default useQrCodeReward;
