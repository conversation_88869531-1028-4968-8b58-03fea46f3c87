import React, { memo } from 'react';
import QRCode from 'react-native-qrcode-svg';
import {
  BlockView,
  ColorsV2,
  CText,
  DateTimeHelpers,
  <PERSON>ceHelper,
  FontSizes,
  IconImage,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';

import { icCopy } from '@images';

import useQrCodeReward from './hook';

interface QrCodeRewardProps {
  code: string;
  expired: string;
}

const QrCodeReward: React.FC<QrCodeRewardProps> = ({ code, expired }) => {
  const { t, handleCopy } = useQrCodeReward(code);

  return (
    <BlockView
      gap={Spacing.SPACE_12}
      padding={{ bottom: Spacing.SPACE_32 }}
    >
      <BlockView
        row
        horizontal
        jBetween
        padding={{ horizontal: Spacing.SPACE_08, vertical: Spacing.SPACE_16 }}
        gap={Spacing.SPACE_08}
      >
        <CText
          color={ColorsV2.orange500}
          size={FontSizes.SIZE_18}
          bold
          numberOfLines={1}
        >
          {code}
        </CText>
        <TouchableOpacity onPress={handleCopy}>
          <IconImage
            source={icCopy}
            size={24}
          />
        </TouchableOpacity>
      </BlockView>

      <BlockView
        row
        horizontal
        jBetween
        gap={Spacing.SPACE_08}
        margin={{ top: Spacing.SPACE_04 }}
      >
        <BlockView
          width={153 * DeviceHelper.WIDTH_RATIO}
          height={1}
          backgroundColor={ColorsV2.neutral100}
        />
        <CText
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral200}
        >
          {t('OR')}
        </CText>
        <BlockView
          width={153 * DeviceHelper.WIDTH_RATIO}
          height={1}
          backgroundColor={ColorsV2.neutral100}
        />
      </BlockView>
      <BlockView
        gap={Spacing.SPACE_08}
        center
      >
        <CText
          size={FontSizes.SIZE_16}
          color={ColorsV2.neutral400}
        >
          {t('SCAN_QR_CODE')}
        </CText>
        <QRCode
          value={code}
          size={170 * DeviceHelper.WIDTH_RATIO}
          logoBackgroundColor="transparent"
        />
        <CText
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral400}
        >
          {t('EXPIRY_DATE')}
          <CText
            semiBold
            color={ColorsV2.neutral800}
          >
            {DateTimeHelpers.formatToString({
              date: expired,
              typeFormat: TypeFormatDate.HourMinuteDate,
            })}
          </CText>
        </CText>
      </BlockView>
    </BlockView>
  );
};

export default memo(QrCodeReward);
