import React from 'react';
import {
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FastImage,
  FontSizes,
  getTextWithLocale,
  HeaderAnimated,
  Spacing,
} from '@btaskee/design-system';
import RewardDetailSkeleton from '@src/components/RewardDetailSkeleton';
import RewardInformation from '@src/components/RewardInformation';
import get from 'lodash-es/get';

import QrCodeReward from './components/QrCodeReward';
import useMyRewardDetailScreen, { MyRewardDetailScreenProps } from './hook';
import styles from './styles';

export const MyRewardDetailScreen: React.FC<MyRewardDetailScreenProps> = (
  props,
) => {
  const { data, navigation, isFetching } = useMyRewardDetailScreen(props);

  return (
    <HeaderAnimated
      backgroundImage={get(data, 'image', '')}
      onPressBack={navigation.goBack}
    >
      <ConditionView
        condition={isFetching}
        viewTrue={<RewardDetailSkeleton />}
        viewFalse={
          <BlockView
            gap={Spacing.SPACE_16}
            padding={{
              horizontal: Spacing.SPACE_16,
              bottom: Spacing.SPACE_60,
            }}
          >
            <BlockView
              row
              horizontal
              gap={Spacing.SPACE_08}
            >
              <FastImage
                source={{ uri: get(data, 'brandInfo.image') }}
                style={styles.brandImage}
              />
              <CText
                size={FontSizes.SIZE_18}
                lineHeight={26}
                bold
                color={ColorsV2.neutral800}
                flex={1}
              >
                {getTextWithLocale(get(data, 'title'))}
              </CText>
            </BlockView>

            <QrCodeReward
              code={get(data, 'promotionCode', '')}
              expired={get(data, 'expired', '')}
            />
            <RewardInformation
              content={getTextWithLocale(get(data, 'content'))}
              note={getTextWithLocale(get(data, 'note'))}
            />
          </BlockView>
        }
      />
    </HeaderAnimated>
  );
};
