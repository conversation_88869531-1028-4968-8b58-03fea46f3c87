import { EndpointKeys, useApiQuery, useAppStore } from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { RouteName } from '@src/navigation/RouteName';
import { RootStackScreenProps } from '@src/navigation/types';

export type MyRewardDetailScreenProps = RootStackScreenProps<
  typeof RouteName.MyRewardDetail
>;

const useMyRewardDetailScreen = ({
  navigation,
  route,
}: MyRewardDetailScreenProps) => {
  const { t, i18n } = useI18n();
  const { giftId } = route.params;
  const { isoCode } = useAppStore();

  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getGiftDetail,
    params: {
      _id: giftId,
      isoCode: isoCode,
    },
  });

  return { navigation, t, i18n, data, isFetching };
};

export default useMyRewardDetailScreen;
