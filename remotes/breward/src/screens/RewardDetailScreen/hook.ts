import { EndpointKeys, useApiQuery } from '@btaskee/design-system';
import { useI18n } from '@src/hooks';
import { RouteName } from '@src/navigation/RouteName';
import { RootStackScreenProps } from '@src/navigation/types';

export type RewardDetailScreenProps = RootStackScreenProps<
  typeof RouteName.RewardDetail
>;

const useRewardDetailScreen = ({
  navigation,
  route,
}: RewardDetailScreenProps) => {
  const { t, i18n } = useI18n();

  const { data, isFetching } = useApiQuery({
    key: EndpointKeys.getRewardDetail,
    params: {
      id: route.params.rewardId,
    },
  });

  return {
    navigation,
    t,
    i18n,
    data,
    isFetching,
  };
};

export default useRewardDetailScreen;
