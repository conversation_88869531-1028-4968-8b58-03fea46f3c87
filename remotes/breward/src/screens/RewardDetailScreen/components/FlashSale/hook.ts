import { useState } from 'react';
import { DateTimeHelpers, IDate } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

interface FlashSaleProps {
  date: IDate;
}

const useFlashSale = ({ date }: FlashSaleProps) => {
  const [diffSecond, setDiffSecond] = useState(
    DateTimeHelpers.getDiff({
      firstDate: date,
      secondDate: DateTimeHelpers.toDayTz({}),
      unit: 'second',
    }),
  );

  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  return { navigation, t, i18n, diffSecond, setDiffSecond };
};

export default useFlashSale;
