import React, { memo } from 'react';
import {
  BlockView,
  ColorsV2,
  CountDown,
  CText,
  FastImage,
  FontSizes,
} from '@btaskee/design-system';

import { bgFlashSale } from '@images';

import useFlashSale from './hook';
import styles from './styles';

interface FlashSaleProps {
  endDate: string;
}
const FlashSale = ({ endDate }: FlashSaleProps) => {
  const { diffSecond, setDiffSecond } = useFlashSale({ date: endDate });

  if (diffSecond <= 0) return null;

  return (
    <BlockView
      row
      jBetween
      center
    >
      <FastImage
        source={bgFlashSale}
        style={styles.bgFlashSale}
      >
        <CText
          size={FontSizes.SIZE_18}
          color={ColorsV2.orange500}
          bold
          // eslint-disable-next-line react-native/no-raw-text
        >
          Flash Sale 🔥
        </CText>
        <CountDown
          until={diffSecond}
          onFinish={() => {
            setTimeout(() => {
              setDiffSecond(0);
            }, 1000);
          }}
          timeToShow={['D', 'H', 'M', 'S']}
          timeLabels={{
            d: undefined,
            h: undefined,
            m: undefined,
            s: undefined,
          }}
          showSeparator={true}
        />
      </FastImage>
    </BlockView>
  );
};

export default memo(FlashSale);
