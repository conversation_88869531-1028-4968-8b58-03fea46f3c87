import { useCallback, useRef } from 'react';
import { BottomModalHandle, useUserStore } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

const useFooterContent = () => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  const { user } = useUserStore();

  const redeemmBottomSheetRef = useRef<BottomModalHandle>(null);
  const signInBottomSheetRef = useRef<BottomModalHandle>(null);

  const onPressRedeem = useCallback(() => {
    if (!user) {
      return signInBottomSheetRef.current?.present();
    }
    redeemmBottomSheetRef.current?.present();
  }, [user]);

  return {
    navigation,
    t,
    i18n,
    redeemmBottomSheetRef,
    signInBottomSheetRef,
    onPressRedeem,
    point: user?.point || 0,
  };
};

export default useFooterContent;
