import React, { memo } from 'react';
import {
  BlockView,
  ColorsV2,
  PrimaryButton,
  Spacing,
} from '@btaskee/design-system';

import RedeemBottomSheet from '../RedeemBottomSheet';
import SignInBottomSheet from '../SignInBottomSheet';
import useFooterContent from './hook';

interface FooterContentProps {
  point: number;
  isFetching: boolean;
}

const FooterContent = (props: FooterContentProps) => {
  const {
    t,
    signInBottomSheetRef,
    redeemmBottomSheetRef,
    onPressRedeem,
    point,
  } = useFooterContent();

  return (
    <BlockView
      inset={'bottom'}
      backgroundColor={ColorsV2.neutralWhite}
      padding={Spacing.SPACE_16}
    >
      <PrimaryButton
        onPress={onPressRedeem}
        disabled={props.isFetching || point < props.point}
        title={t('REDEEM_VOUCHER')}
      />

      <RedeemBottomSheet bottomSheetRef={redeemmBottomSheetRef} />

      <SignInBottomSheet bottomSheetRef={signInBottomSheetRef} />
    </BlockView>
  );
};

export default memo(FooterContent);
