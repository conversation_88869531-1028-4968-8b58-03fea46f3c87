import React, { memo } from 'react';
import {
  BlockView,
  BottomModal,
  BottomModalHandle,
  ColorsV2,
  CText,
  DeviceHelper,
  <PERSON>ontSizes,
  <PERSON>tie,
  PrimaryButton,
  Spacing,
  Trans,
} from '@btaskee/design-system';
import { dealLottie } from '@src/assets/lotties';
import { NAME_SPACE, useI18n } from '@src/hooks';

import styles from './styles';

interface RedeemBottomSheetProps {
  bottomSheetRef: React.RefObject<BottomModalHandle>;
}

const RedeemBottomSheet = ({ bottomSheetRef }: RedeemBottomSheetProps) => {
  const { t } = useI18n();

  return (
    <BottomModal
      handleIndicatorStyle={styles.handleIndicator}
      modalRef={bottomSheetRef}
    >
      <BlockView
        center
        padding={Spacing.SPACE_16}
      >
        <Lottie
          source={dealLottie}
          autoPlay={true}
          count={3}
          style={styles.lottie}
        />
        <CText
          size={FontSizes.SIZE_16}
          bold
          color={ColorsV2.neutral800}
          lineHeight={24}
          margin={{ top: Spacing.SPACE_24 }}
        >
          {t('REDEEM')}
        </CText>
        <CText
          margin={{ top: Spacing.SPACE_08 }}
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral400}
          lineHeight={20}
          center
          width={252 * DeviceHelper.WIDTH_RATIO}
        >
          <Trans
            i18nKey={'CONFIRM_REDEEM'}
            ns={NAME_SPACE}
            values={{ point: 40 }}
            components={{
              tag: (
                <CText
                  color={ColorsV2.orange500}
                  bold
                />
              ),
            }}
          />
        </CText>

        <BlockView
          row
          horizontal
          jBetween
          margin={{ top: Spacing.SPACE_24 }}
          gap={Spacing.SPACE_16}
        >
          <PrimaryButton
            type="secondary"
            title={t('CANCEL')}
            style={styles.button}
          />

          <PrimaryButton
            title={t('CONFIRM')}
            style={styles.button}
          />
        </BlockView>
      </BlockView>
    </BottomModal>
  );
};

export default memo(RedeemBottomSheet);
