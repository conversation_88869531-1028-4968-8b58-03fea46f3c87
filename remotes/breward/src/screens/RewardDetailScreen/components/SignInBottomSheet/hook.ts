import React, { useCallback } from 'react';
import {
  BottomModalHandle,
  NavigationService,
  RouteName,
} from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';

export interface SignInBottomSheetProps {
  bottomSheetRef: React.RefObject<BottomModalHandle>;
}
const useSignInBottomSheet = ({ bottomSheetRef }: SignInBottomSheetProps) => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  const onPressSignIn = useCallback(() => {
    bottomSheetRef.current?.dismiss();
    NavigationService.navigate(RouteName.Auth, {
      screen: 'Login',
    });
  }, [bottomSheetRef]);

  const onPressSignUp = useCallback(() => {
    bottomSheetRef.current?.dismiss();
    NavigationService.navigate(RouteName.Auth, {
      screen: 'Register',
    });
  }, [bottomSheetRef]);

  return { navigation, t, i18n, onPressSignIn, onPressSignUp };
};

export default useSignInBottomSheet;
