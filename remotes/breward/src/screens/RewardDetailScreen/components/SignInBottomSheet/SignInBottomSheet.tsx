import React, { memo } from 'react';
import {
  BlockView,
  BottomModal,
  ColorsV2,
  CText,
  <PERSON>ceHelper,
  FontSizes,
  IconImage,
  PrimaryButton,
  Spacing,
} from '@btaskee/design-system';

import { icSignIn } from '@images';

import useSignInBottomSheet, { SignInBottomSheetProps } from './hook';
import styles from './styles';

const SignInBottomSheet = ({ bottomSheetRef }: SignInBottomSheetProps) => {
  const { t, onPressSignIn, onPressSignUp } = useSignInBottomSheet({
    bottomSheetRef,
  });

  return (
    <BottomModal
      handleIndicatorStyle={styles.handleIndicator}
      modalRef={bottomSheetRef}
    >
      <BlockView
        center
        padding={Spacing.SPACE_16}
      >
        <IconImage
          source={icSignIn}
          size={100 * DeviceHelper.WIDTH_RATIO}
        />
        <CText
          size={FontSizes.SIZE_16}
          bold
          color={ColorsV2.neutral800}
          lineHeight={24}
          margin={{ top: Spacing.SPACE_24 }}
          center
        >
          {t('NOT_SIGNED_IN')}
        </CText>
        <CText
          margin={{ top: Spacing.SPACE_08 }}
          size={FontSizes.SIZE_14}
          color={ColorsV2.neutral400}
          lineHeight={20}
          center
        >
          {t('PLEASE_SIGN_IN_TO_CONTINUE')}
        </CText>

        <BlockView
          margin={{ top: Spacing.SPACE_24 }}
          gap={Spacing.SPACE_16}
          width={'100%'}
        >
          <PrimaryButton
            onPress={onPressSignIn}
            title={t('SIGN_IN')}
          />

          <PrimaryButton
            type="secondary"
            onPress={onPressSignUp}
            title={t('SIGN_UP')}
          />
        </BlockView>
      </BlockView>
    </BottomModal>
  );
};

export default memo(SignInBottomSheet);
