import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  FastImage,
  FontSizes,
  getTextWithLocale,
  HeaderAnimated,
  IconImage,
  Spacing,
  TypeFormatDate,
} from '@btaskee/design-system';
import RewardDetailSkeleton from '@src/components/RewardDetailSkeleton';
import RewardInformation from '@src/components/RewardInformation';
import { get } from 'lodash-es';

import { icBadge } from '@images';

import FlashSale from './components/FlashSale';
import FooterContent from './components/FooterContent';
import useRewardDetailScreen, { RewardDetailScreenProps } from './hook';
import styles from './styles';

export const RewardDetailScreen: React.FC<RewardDetailScreenProps> = (
  props,
) => {
  const { t, data, isFetching, navigation } = useRewardDetailScreen(props);

  console.log('RewardDetailScreen data: ', get(data, 'brandInfo.image'));
  return (
    <HeaderAnimated
      backgroundImage={get(data, 'image', '')}
      onPressBack={navigation.goBack}
      footer={
        <FooterContent
          point={get(data, 'point', 0)}
          isFetching={isFetching}
        />
      }
    >
      <ConditionView
        condition={isFetching}
        viewTrue={<RewardDetailSkeleton />}
        viewFalse={
          <BlockView
            gap={Spacing.SPACE_16}
            padding={{
              horizontal: Spacing.SPACE_16,
              bottom: Spacing.SPACE_60,
            }}
          >
            <BlockView
              row
              horizontal
              gap={Spacing.SPACE_08}
            >
              <FastImage
                source={{ uri: null }}
                style={styles.brandImage}
              />
              <CText
                size={FontSizes.SIZE_18}
                lineHeight={26}
                bold
                color={ColorsV2.neutral800}
                flex={1}
              >
                {getTextWithLocale(get(data, 'title'))}
              </CText>
            </BlockView>

            <BlockView
              padding={Spacing.SPACE_08}
              row
              horizontal
              radius={BorderRadius.RADIUS_08}
              jBetween
              gap={Spacing.SPACE_08}
              border={{
                width: 1,
                color: ColorsV2.neutral100,
              }}
            >
              <BlockView
                gap={Spacing.SPACE_04}
                flex={1}
              >
                <CText
                  color={ColorsV2.neutral400}
                  size={FontSizes.SIZE_12}
                >
                  {t('EXCHANGE')}
                </CText>
                <BlockView
                  row
                  horizontal
                  gap={Spacing.SPACE_08}
                >
                  <IconImage
                    source={icBadge}
                    size={16}
                  />
                  <CText
                    size={FontSizes.SIZE_14}
                    bold
                    color={ColorsV2.orange500}
                  >
                    {get(data, 'point', 0)}
                  </CText>
                </BlockView>
              </BlockView>
              <BlockView
                width={1}
                height={46}
                backgroundColor={ColorsV2.neutral50}
              />

              <BlockView
                gap={Spacing.SPACE_04}
                flex={1}
                right
              >
                <CText
                  color={ColorsV2.neutral400}
                  size={FontSizes.SIZE_12}
                >
                  {t('EXPIRATION_DATE')}
                </CText>

                <CText
                  size={FontSizes.SIZE_14}
                  semiBold
                  color={ColorsV2.neutral800}
                >
                  {DateTimeHelpers.formatToString({
                    date: get(data, 'endDate', ''),
                    typeFormat: TypeFormatDate.DateShort,
                  })}
                </CText>
              </BlockView>
            </BlockView>

            <FlashSale endDate={'2025-08-02T05:20:09.268Z'} />
            <RewardInformation
              content={getTextWithLocale(get(data, 'content'))}
              note={getTextWithLocale(get(data, 'note'))}
            />
          </BlockView>
        }
      />
    </HeaderAnimated>
  );
};
