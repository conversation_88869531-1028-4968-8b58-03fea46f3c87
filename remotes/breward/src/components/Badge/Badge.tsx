import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

interface BadgeProps {
  count: number;
  maxCount?: number;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  textColor?: string;
  position?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  showZero?: boolean;
  dot?: boolean;
}

/**
 * Badge component for displaying notification counts or status indicators
 * Purpose: Shows numeric badges or dot indicators on UI elements like icons or buttons
 * @param count - The number to display in the badge
 * @param maxCount - Maximum number to display before showing "99+" format (default: 99)
 * @param size - Size variant of the badge: 'small', 'medium', or 'large' (default: 'small')
 * @param color - Background color of the badge (default: ColorsV2.red500)
 * @param textColor - Text color of the badge (default: ColorsV2.neutralWhite)
 * @param position - Custom positioning object with top, right, bottom, left values
 * @param showZero - Whether to show badge when count is 0 (default: false)
 * @param dot - Show as a dot indicator without text (default: false)
 * @returns {JSX.Element | null} Badge component or null if count is 0 and showZero is false
 */
const Badge = ({
  count,
  maxCount = 99,
  size = 'small',
  color = ColorsV2.red500,
  textColor = ColorsV2.neutralWhite,
  position,
  showZero = false,
  dot = false,
}: BadgeProps) => {
  // Don't render if count is 0 and showZero is false
  if (count === 0 && !showZero) {
    return null;
  }

  // Size configurations
  const sizeConfig = {
    small: {
      width: dot ? Spacing.SPACE_08 : Spacing.SPACE_16,
      height: dot ? Spacing.SPACE_08 : Spacing.SPACE_16,
      fontSize: FontSizes.SIZE_10,
      minWidth: Spacing.SPACE_16,
    },
    medium: {
      width: dot ? Spacing.SPACE_12 : Spacing.SPACE_20,
      height: dot ? Spacing.SPACE_12 : Spacing.SPACE_20,
      fontSize: FontSizes.SIZE_12,
      minWidth: Spacing.SPACE_20,
    },
    large: {
      width: dot ? Spacing.SPACE_16 : Spacing.SPACE_24,
      height: dot ? Spacing.SPACE_16 : Spacing.SPACE_24,
      fontSize: FontSizes.SIZE_14,
      minWidth: Spacing.SPACE_24,
    },
  };

  const config = sizeConfig[size];

  // Format display text
  const displayText = count > maxCount ? `${maxCount}+` : count.toString();

  // Default positioning
  const defaultPosition = {
    top: -4,
    right: -4,
    bottom: undefined,
    left: undefined,
  };

  const finalPosition = position || defaultPosition;

  return (
    <BlockView
      backgroundColor={color}
      radius={BorderRadius.RADIUS_FULL}
      center
      width={dot ? config.width : Math.max(config.minWidth, config.width)}
      height={config.height}
      absolute
      positionTop={finalPosition.top}
      positionRight={finalPosition.right}
      positionBottom={finalPosition.bottom}
      positionLeft={finalPosition.left}
      padding={dot ? undefined : { horizontal: Spacing.SPACE_04 }}
    >
      {!dot && (
        <CText
          size={config.fontSize}
          semiBold
          color={textColor}
          numberOfLines={1}
        >
          {displayText}
        </CText>
      )}
    </BlockView>
  );
};

export default memo(Badge);
