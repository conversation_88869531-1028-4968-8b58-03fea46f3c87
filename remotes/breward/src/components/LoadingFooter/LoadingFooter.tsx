import React, { memo } from 'react';
import { ActivityIndicator } from 'react-native';
import { BlockView, ColorsV2, Spacing } from '@btaskee/design-system';

/**
 * Loading footer component for pagination
 * Purpose: Shows loading indicator when loading more used rewards
 * @param isVisible - Whether to show the loading footer
 * @returns {JSX.Element | null} Loading footer or null
 */
const LoadingFooter = ({ isVisible }: { isVisible: boolean }) => {
  if (!isVisible) return null;

  return (
    <BlockView
      padding={{ vertical: Spacing.SPACE_16 }}
      center
      testID="loading-footer"
    >
      <ActivityIndicator
        size="small"
        color={ColorsV2.orange500}
      />
    </BlockView>
  );
};

export default memo(LoadingFooter);
