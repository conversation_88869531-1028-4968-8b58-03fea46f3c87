import React, { memo } from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  SkeletonBox,
  Spacing,
} from '@btaskee/design-system';

import styles from './styles';

/**
 * Skeleton loading component for Reward Detail screen
 * Purpose: Shows skeleton placeholders while reward detail data is loading
 * @returns {JSX.Element} Skeleton layout matching the reward detail structure
 */
const RewardDetailSkeleton = () => {
  return (
    <BlockView
      gap={Spacing.SPACE_16}
      padding={{
        horizontal: Spacing.SPACE_16,
        bottom: Spacing.SPACE_60,
      }}
    >
      {/* Brand and Title Section */}
      <BlockView
        row
        horizontal
        gap={Spacing.SPACE_08}
      >
        <SkeletonBox style={styles.brandImage} />
        <BlockView
          flex={1}
          gap={Spacing.SPACE_04}
        >
          <SkeletonBox style={styles.titleLine1} />
          <SkeletonBox style={styles.titleLine2} />
        </BlockView>
      </BlockView>

      {/* Exchange and Expiration Section */}
      <BlockView
        padding={Spacing.SPACE_08}
        row
        horizontal
        radius={BorderRadius.RADIUS_08}
        jBetween
        gap={Spacing.SPACE_08}
        border={{
          width: 1,
          color: ColorsV2.neutral100,
        }}
      >
        {/* Exchange Section */}
        <BlockView
          gap={Spacing.SPACE_04}
          flex={1}
        >
          <SkeletonBox style={styles.exchangeLabel} />
          <BlockView
            row
            horizontal
            gap={Spacing.SPACE_08}
          >
            <SkeletonBox style={styles.badgeIcon} />
            <SkeletonBox style={styles.pointValue} />
          </BlockView>
        </BlockView>

        {/* Divider */}
        <BlockView
          width={1}
          height={46}
          backgroundColor={ColorsV2.neutral50}
        />

        {/* Expiration Section */}
        <BlockView
          gap={Spacing.SPACE_04}
          flex={1}
          right
        >
          <SkeletonBox style={styles.expirationLabel} />
          <SkeletonBox style={styles.expirationDate} />
        </BlockView>
      </BlockView>

      {/* Flash Sale Section */}
      <BlockView
        row
        jBetween
        center
      >
        <SkeletonBox style={styles.flashSale} />
      </BlockView>

      {/* Reward Information Section */}
      <BlockView gap={Spacing.SPACE_12}>
        <SkeletonBox style={styles.infoTitle} />
        <SkeletonBox style={styles.infoLine1} />
        <SkeletonBox style={styles.infoLine2} />
        <SkeletonBox style={styles.infoLine3} />
        <SkeletonBox style={styles.noteTitle} />
        <SkeletonBox style={styles.noteLine1} />
        <SkeletonBox style={styles.noteLine2} />
      </BlockView>
    </BlockView>
  );
};

export default memo(RewardDetailSkeleton);
