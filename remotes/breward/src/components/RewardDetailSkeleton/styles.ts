import { StyleSheet } from 'react-native';
import { BorderRadius, Spacing } from '@btaskee/design-system';

export default StyleSheet.create({
  // Brand and Title Section
  brandImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.RADIUS_08,
  },
  titleLine1: {
    width: '100%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },
  titleLine2: {
    width: '70%',
    height: 20,
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Exchange Section
  exchangeLabel: {
    width: 60,
    height: 12,
    borderRadius: BorderRadius.RADIUS_04,
  },
  badgeIcon: {
    width: 16,
    height: 16,
    borderRadius: BorderRadius.RADIUS_04,
  },
  pointValue: {
    width: 40,
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Expiration Section
  expirationLabel: {
    width: 80,
    height: 12,
    borderRadius: BorderRadius.RADIUS_04,
  },
  expirationDate: {
    width: 70,
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },

  // Flash Sale Section
  flashSale: {
    width: '100%',
    height: 80,
    borderRadius: BorderRadius.RADIUS_08,
  },

  // Reward Information Section
  infoTitle: {
    width: 120,
    height: 16,
    borderRadius: BorderRadius.RADIUS_04,
  },
  infoLine1: {
    width: '100%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
  infoLine2: {
    width: '90%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
  infoLine3: {
    width: '60%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
  noteTitle: {
    width: 80,
    height: 16,
    borderRadius: BorderRadius.RADIUS_04,
    marginTop: Spacing.SPACE_08,
  },
  noteLine1: {
    width: '85%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
  noteLine2: {
    width: '50%',
    height: 14,
    borderRadius: BorderRadius.RADIUS_04,
  },
});
