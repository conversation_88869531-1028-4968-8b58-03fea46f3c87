import React, { memo } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

interface RewardInformationProps {
  content: string;
  note: string;
}

const RewardInformation: React.FC<RewardInformationProps> = ({
  content,
  note,
}) => {
  const { t } = useI18n();

  if (!content && !note) return null;

  return (
    <BlockView gap={Spacing.SPACE_12}>
      {content && (
        <BlockView gap={Spacing.SPACE_08}>
          <CText
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral800}
            bold
          >
            {t('REWARD_DETAILS')}
          </CText>
          <CText
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral800}
            lineHeight={20}
          >
            {content}
          </CText>
        </BlockView>
      )}
      {note && (
        <BlockView gap={Spacing.SPACE_08}>
          <CText
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral800}
            bold
          >
            {t('TERMS_AND_CONDITIONS')}
          </CText>
          <CText
            size={FontSizes.SIZE_14}
            color={ColorsV2.neutral800}
            lineHeight={20}
          >
            {note}
          </CText>
        </BlockView>
      )}
    </BlockView>
  );
};

export default memo(RewardInformation);
