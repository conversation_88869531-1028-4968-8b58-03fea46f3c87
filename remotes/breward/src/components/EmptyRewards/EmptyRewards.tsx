import React, { memo } from 'react';
import {
  BlockView,
  ColorsV2,
  CT<PERSON>t,
  DeviceHelper,
  FastImage,
  FastImageComponentProps,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

import { emptyVoucher } from '@images';

interface EmptyRewardsProps {
  title: string;
  description: string;
  source?: FastImageComponentProps['source'];
}

const EmptyRewards = ({ title, description, source }: EmptyRewardsProps) => {
  return (
    <BlockView
      maxHeight={DeviceHelper.WINDOW.HEIGHT}
      center
      padding={{
        horizontal: Spacing.SPACE_24,
        vertical: 120 * DeviceHelper.WIDTH_RATIO,
      }}
    >
      <FastImage
        source={source ?? emptyVoucher}
        style={{
          width: 140 * DeviceHelper.WIDTH_RATIO,
          height: 140 * DeviceHelper.WIDTH_RATIO,
        }}
      />
      <CText
        margin={{ top: Spacing.SPACE_24 }}
        center
        bold
        color={ColorsV2.neutral800}
        lineHeight={Spacing.SPACE_36}
        size={FontSizes.SIZE_24}
      >
        {title}
      </CText>
      <CText
        margin={{ top: Spacing.SPACE_08 }}
        center
        color={ColorsV2.neutral400}
        lineHeight={Spacing.SPACE_20}
        size={FontSizes.SIZE_14}
      >
        {description}
      </CText>
    </BlockView>
  );
};

export default memo(EmptyRewards);
