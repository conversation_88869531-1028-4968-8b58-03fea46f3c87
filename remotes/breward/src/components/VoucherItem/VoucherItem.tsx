import React, { memo, useCallback } from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  DateTimeHelpers,
  DeviceHelper,
  FastImage,
  FontSizes,
  getTextWithLocale,
  PromotionItem,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';
import DashedLine from '@src/components/DashedLine';

import useVoucherItem from './hook';
import styles from './styles';

interface VoucherItemProps extends PromotionItem {
  containerStyle?: StyleProp<ViewStyle>;
  dashColor?: string;
}

const VoucherItem = (props: VoucherItemProps) => {
  const { t, handleNavigation } = useVoucherItem(props);

  const { title, brandInfo, content, expired, containerStyle, dashColor } =
    props;

  const renderItemSeparator = useCallback(() => {
    return (
      <BlockView
        gap={Spacing.SPACE_04}
        jBetween
        horizontal
        row
        margin={{ horizontal: -8 }}
      >
        <BlockView
          width={16}
          height={16}
          radius={BorderRadius.RADIUS_FULL}
          backgroundColor={ColorsV2.neutralWhite}
          border={{
            width: 1,
            color: ColorsV2.neutral100,
          }}
        />

        <DashedLine
          dashColor={dashColor || ColorsV2.neutral100}
          dashGap={6}
          dashLength={6}
          width={334 * DeviceHelper.WIDTH_RATIO}
        />

        <BlockView
          width={16}
          height={16}
          radius={BorderRadius.RADIUS_FULL}
          backgroundColor={ColorsV2.neutralWhite}
          border={{
            width: 1,
            color: ColorsV2.neutral100,
          }}
        />
      </BlockView>
    );
  }, [dashColor]);

  return (
    <TouchableOpacity
      overflow="hidden"
      onPress={handleNavigation}
    >
      <BlockView
        radius={BorderRadius.RADIUS_08}
        width={'100%'}
        border={{
          width: 1,
          color: ColorsV2.neutral100,
        }}
        style={containerStyle}
      >
        {/* Header */}
        <BlockView gap={Spacing.SPACE_08}>
          {/* Brand Info */}
          <BlockView
            margin={{ horizontal: Spacing.SPACE_12, top: Spacing.SPACE_12 }}
            horizontal
            row
          >
            <BlockView
              row
              gap={Spacing.SPACE_08}
              horizontal
              radius={BorderRadius.RADIUS_04}
            >
              <FastImage
                source={{ uri: brandInfo?.image }}
                style={styles.brandImage}
              />
              <CText
                size={FontSizes.SIZE_14}
                lineHeight={20}
                color={ColorsV2.neutral800}
                numberOfLines={1}
              >
                {getTextWithLocale(brandInfo.text)}
              </CText>
            </BlockView>
          </BlockView>

          {/* Seperator */}

          {renderItemSeparator()}

          {/* Footer */}

          <BlockView
            padding={{ horizontal: Spacing.SPACE_12, bottom: Spacing.SPACE_12 }}
            gap={Spacing.SPACE_08}
          >
            <CText
              size={FontSizes.SIZE_14}
              bold
              color={ColorsV2.green500}
              numberOfLines={2}
            >
              {getTextWithLocale(title)}
            </CText>
            <CText
              size={FontSizes.SIZE_14}
              color={ColorsV2.neutral400}
              lineHeight={20}
              numberOfLines={2}
            >
              {getTextWithLocale(content)}
            </CText>

            <BlockView
              horizontal
              jBetween
              row
            >
              <CText
                size={FontSizes.SIZE_12}
                color={ColorsV2.neutral200}
                numberOfLines={1}
              >
                {t('EXPIRED_DATE', {
                  day: DateTimeHelpers.formatToString({
                    date: expired,
                    typeFormat: TypeFormatDate.DateShort,
                  }),
                })}
              </CText>
              {/* footer right */}
              <BlockView />
            </BlockView>
          </BlockView>
        </BlockView>
      </BlockView>
    </TouchableOpacity>
  );
};

export default memo(VoucherItem);
