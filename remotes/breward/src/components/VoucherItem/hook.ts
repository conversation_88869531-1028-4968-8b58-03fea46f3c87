import { useCallback } from 'react';
import { PromotionItem } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';
import { RouteName } from '@src/navigation/RouteName';

const useMyHookName = (props: PromotionItem) => {
  const { t, i18n } = useI18n();
  const navigation = useAppNavigation();

  const handleNavigation = useCallback(() => {
    if (props.type === 'MARKETING_CAMPAIGN') {
      navigation.navigate(RouteName.ExclusiveBrandDetail, {
        brandId: props._id,
      });
    } else {
      navigation.navigate(RouteName.MyRewardDetail, { giftId: props._id });
    }
  }, [navigation, props._id, props.type]);

  return { navigation, t, i18n, handleNavigation };
};

export default useMyHookName;
