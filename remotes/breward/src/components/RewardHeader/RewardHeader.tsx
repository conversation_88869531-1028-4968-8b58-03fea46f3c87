import React, { memo } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  FastImage,
  IconAssets,
  IconImage,
  ThemeHelpers,
  TouchableOpacity,
} from '@btaskee/design-system';
import { useAppNavigation } from '@src/hooks';

import styles, { height } from './styles';

interface RewardHeaderProps {
  backgroundImage: string;
  scrollY: SharedValue<number>;
}

const RewardHeader: React.FC<RewardHeaderProps> = ({
  backgroundImage,
  scrollY,
}) => {
  const navigation = useAppNavigation();

  const animatedLinearStyle = useAnimatedStyle(() => {
    const yValue = interpolate(
      scrollY.value,
      [0, 200],
      [0, 200],
      Extrapolation.CLAMP,
    );
    return {
      height: height - yValue,
      overflow: 'hidden',
    };
  });

  return (
    <Animated.View style={animatedLinearStyle}>
      <LinearGradient
        colors={[
          ThemeHelpers.hexWithOpacity(ColorsV2.neutralWhite, 0),
          ColorsV2.neutralWhite,
        ]}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
        locations={[0.36, 0.94]}
        style={styles.linearGradient}
      />
      <FastImage
        source={{ uri: backgroundImage }}
        style={styles.image}
        resizeMode="cover"
      />

      <BlockView
        inset={'top'}
        padding={{ horizontal: 16 }}
        zIndex={10}
      >
        <TouchableOpacity
          width={32}
          height={32}
          center
          radius={BorderRadius.RADIUS_08}
          backgroundColor={ColorsV2.neutral50}
          onPress={navigation.goBack}
        >
          <IconImage
            source={IconAssets.icBack}
            size={24}
          />
        </TouchableOpacity>
      </BlockView>
    </Animated.View>
  );
};

export default memo(RewardHeader);
