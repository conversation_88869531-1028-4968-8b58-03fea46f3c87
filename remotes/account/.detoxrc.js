/** @type {Detox.DetoxConfig} */
module.exports = {
  testRunner: {
    args: {
      '$0': 'jest',
      config: 'e2e/jest.config.js'
    },
    jest: {
      setupTimeout: 120000
    }
  },
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: '../../apps/host/ios/build/Build/Products/Debug-iphonesimulator/host.app',
      build: 'cd ../../apps/host && xcodebuild -workspace ios/host.xcworkspace -scheme host -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build'
    },
    'ios.release': {
      type: 'ios.app',
      binaryPath: '../../apps/host/ios/build/Build/Products/Release-iphonesimulator/host.app',
      build: 'cd ../../apps/host && xcodebuild -workspace ios/host.xcworkspace -scheme host -configuration Release -sdk iphonesimulator -derivedDataPath ios/build'
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: '../../apps/host/android/app/build/outputs/apk/debug/app-debug.apk',
      build: 'cd ../../apps/host/android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug',
      reversePorts: [8081, 9002]
    },
    'android.release': {
      type: 'android.apk',
      binaryPath: '../../apps/host/android/app/build/outputs/apk/release/app-release.apk',
      build: 'cd ../../apps/host/android && ./gradlew assembleRelease assembleAndroidTest -DtestBuildType=release'
    }
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 16 Pro Max'
      }
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_6_API_34'
      }
    }
  },
  configurations: {
    ios: {
      device: 'simulator',
      app: 'ios.debug'
    },
    'ios.release': {
      device: 'simulator',
      app: 'ios.release'
    },
    android: {
      device: 'emulator',
      app: 'android.debug'
    },
    'android.release': {
      device: 'emulator',
      app: 'android.release'
    }
  }
};
