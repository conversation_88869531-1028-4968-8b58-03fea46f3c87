{"name": "account", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/account"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/account"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/account"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/account"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/account"}}}, "tags": ["scope:account"]}