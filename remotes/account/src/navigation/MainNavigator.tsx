import '../i18n';

import React, { useCallback } from 'react';
import { TouchableOpacity } from 'react-native';
import {
  ColorsV2,
  FontFamily,
  IconAssets,
  IconImage,
} from '@btaskee/design-system';
import {
  createNativeStackNavigator,
  type NativeStackNavigationOptions,
} from '@react-navigation/native-stack';
import { useI18n } from '@src/hooks';
import {
  AboutbTaskeeScreen,
  BPayScreen,
  DeleteAccountScreen,
  FavoriteTaskerScreen,
  HelpScreen,
  IntroFavoriteTaskerScreen,
  ReferalScreen,
  SavedAddressScreen,
  SettingScreen,
  TaskerBlacklistScreen,
  TopupScreen,
} from '@src/screens';

import { RouteName } from './RouteName';
import { RootStackParamList } from './types';

const Stack = createNativeStackNavigator<RootStackParamList>();

const MainNavigator = () => {
  const { t } = useI18n();

  const renderHeaderLeft = useCallback((navigation: any) => {
    return (
      <TouchableOpacity
        onPress={() => navigation?.goBack()}
        activeOpacity={0.7}
      >
        <IconImage
          source={IconAssets.icBack}
          size={24}
          color={ColorsV2.neutral800}
        />
      </TouchableOpacity>
    );
  }, []);

  return (
    <Stack.Navigator
      screenOptions={({ navigation }): NativeStackNavigationOptions => ({
        headerShown: true,
        headerLeft: () => renderHeaderLeft(navigation),
        animation: 'slide_from_right',
        animationDuration: 200,
        contentStyle: { backgroundColor: ColorsV2.neutralWhite },
        headerStyle: {
          backgroundColor: ColorsV2.neutralWhite,
        },
        headerTitleStyle: {
          color: ColorsV2.neutral800,
          fontSize: 18,
          fontFamily: FontFamily.bold,
        },
      })}
      initialRouteName={RouteName.AboutbTaskee}
    >
      <Stack.Screen
        name={RouteName.AboutbTaskee}
        component={AboutbTaskeeScreen}
        options={{
          headerShown: true,
          title: t('ABOUT_BTASKEE'),
        }}
      />
      <Stack.Screen
        name={RouteName.SavedAddress}
        component={SavedAddressScreen}
        options={{
          headerShown: true,
          title: t('SAVED_ADDRESS'),
        }}
      />
      <Stack.Screen
        name={RouteName.DeleteAccount}
        component={DeleteAccountScreen}
        options={{
          headerShown: true,
          title: t('DELETE_ACCOUNT'),
        }}
      />
      <Stack.Screen
        name={RouteName.Help}
        component={HelpScreen}
        options={{
          headerShown: true,
          title: t('HELP'),
        }}
      />
      <Stack.Screen
        name={RouteName.Referal}
        component={ReferalScreen}
        options={{
          headerShown: true,
          title: t('REFERAL'),
        }}
      />
      <Stack.Screen
        name={RouteName.Setting}
        component={SettingScreen}
        options={{
          headerShown: true,
          title: t('SETTING'),
        }}
      />
      <Stack.Screen
        name={RouteName.Topup}
        component={TopupScreen}
        options={{
          headerShown: true,
          title: t('TOPUP'),
        }}
      />
      <Stack.Screen
        name={RouteName.BPay}
        component={BPayScreen}
        options={{
          headerShown: true,
          title: t('BPAY'),
        }}
      />
      <Stack.Screen
        name={RouteName.IntroFavoriteTaskerScreen}
        component={IntroFavoriteTaskerScreen}
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name={RouteName.FavoriteTaskerScreen}
        component={FavoriteTaskerScreen}
        options={{
          headerShown: true,
          title: t('FAVORITE_TASKER'),
        }}
      />
      <Stack.Screen
        name={RouteName.TaskerBlacklistScreen}
        component={TaskerBlacklistScreen}
        options={{
          headerShown: true,
          title: t('BLOCK_LIST'),
        }}
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
