import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RouteName } from './RouteName';

export type RootStackParamList = {
  [RouteName.AboutbTaskee]: undefined;
  [RouteName.SavedAddress]: undefined;
  [RouteName.DeleteAccount]: undefined;
  [RouteName.Help]: undefined;
  [RouteName.Referal]: undefined;
  [RouteName.Setting]: undefined;
  [RouteName.Topup]: undefined;
  [RouteName.BPay]: undefined;
  [RouteName.IntroFavoriteTaskerScreen]: undefined;
  [RouteName.FavoriteTaskerScreen]: undefined;
  [RouteName.TaskerBlacklistScreen]: undefined;
};

export type RootStackScreenProps<T extends keyof RootStackParamList> =
  NativeStackNavigationProp<RootStackParamList, T>;
