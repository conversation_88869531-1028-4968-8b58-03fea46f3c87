/**
 * useFadeAnimation Hook
 *
 * A custom hook that provides fade-in and fade-out animations.
 */
import { useCallback, useRef, useState } from 'react';
import { Animated } from 'react-native';

interface FadeAnimationResult {
  opacity: Animated.Value;
  isVisible: boolean;
  isAnimating: boolean;
  fadeIn: (callback?: () => void) => void;
  fadeOut: (callback?: () => void) => void;
}

/**
 * Custom hook for fade animations
 * @param initialVisible Whether the component should be initially visible
 * @param fadeInDuration Duration of the fade-in animation in milliseconds
 * @param fadeOutDuration Duration of the fade-out animation in milliseconds
 * @returns Animation controls and state
 */
export const useFadeAnimation = (
  initialVisible = false,
  fadeInDuration = 400,
  fadeOutDuration = 200,
): FadeAnimationResult => {
  const [isVisible, setIsVisible] = useState(initialVisible);
  const [isAnimating, setIsAnimating] = useState(false);
  const opacity = useRef(new Animated.Value(initialVisible ? 1 : 0)).current;

  const fadeIn = useCallback(
    (callback?: () => void) => {
      setIsAnimating(true);
      setIsVisible(true);

      Animated.timing(opacity, {
        toValue: 1,
        duration: fadeInDuration,
        useNativeDriver: true,
      }).start(() => {
        setIsAnimating(false);
        callback?.();
      });
    },
    [opacity, fadeInDuration],
  );

  const fadeOut = useCallback(
    (callback?: () => void) => {
      setIsAnimating(true);

      Animated.timing(opacity, {
        toValue: 0,
        duration: fadeOutDuration,
        useNativeDriver: true,
      }).start(() => {
        setIsVisible(false);
        setIsAnimating(false);
        callback?.();
      });
    },
    [opacity, fadeOutDuration],
  );

  return {
    opacity,
    isVisible,
    isAnimating,
    fadeIn,
    fadeOut,
  };
};
