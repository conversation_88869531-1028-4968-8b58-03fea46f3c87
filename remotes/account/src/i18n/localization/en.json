{"ACCOUNT": "Account", "PERSONAL_PROFILE": "Personal Profile", "SAVED_ADDRESSES": "Saved Addresses", "TRANSACTION_HISTORY": "Transaction History", "MY_OFFER": "My Offer", "FAVORITE_TASKER": "Favorite Tasker", "BLOCK_LIST": "Block list", "BUSINESS_PROFILE": "Business Profile", "ACCOUNT_BALANCE": "Account <PERSON><PERSON>", "UTILITY": "Utility", "B_PAY": "b<PERSON>ay", "B_REWARDS": "bRewards", "VOUCHER_PACKAGE": "Voucher Package", "INVITE_FRIENDS": "Invite Friends", "SUPPORT": "Support", "HELP": "Help", "SETTING": "Setting", "ABOUT_BTASKEE": "About bTaskee", "RATE_BTASKEE_NOW": "Rate bTaskee Now", "TERMS_OF_USE": "Terms of Use", "PRIVACY_POLICY": "Privacy Policy", "VERSION": "Version {{version}}", "VERSION_CODE": "Build {{versionCode}}", "INTRODUCTION_BUTTON_UNDERSTAND": "I understand", "WHAT_IS_FAV_TASKERS": "What is favorite Tasker?", "WHAT_IS_FAV_TASKERS_ANSWER": "Favorite Taskers are those who have previously completed tasks for you and received a 5-star rating, or those you've added to your favorites list after completing a task.", "WHY_DO_NEED_FAV_TASKERS": "Why do I need favorite Taskers?", "WHY_DO_NEED_FAV_TASKERS_ANSWER_1": "The system will prioritize sending your tasks to Taskers in your favorites list first. Only when your favorite Taskers are all busy, the system will send the task to other Taskers.", "WHY_DO_NEED_FAV_TASKERS_ANSWER_2": "b<PERSON><PERSON><PERSON> encourages you to have as many Taskers as possible in your favorites list, so your tasks will always be handled by those you approve.", "WHY_DO_NEED_FAV_TASKERS_ANSWER_3": "Moreover, you can also chat directly with your favorite Taskers and reschedule with them quickly and conveniently.", "HOW_TO_HAVE_FAV_TASKER": "What is rebook with a favorite Tasker?", "HOW_TO_HAVE_FAV_TASKER_ANSWER_1": "Now, you can rebook with the favorite Taskers.", "HOW_TO_HAVE_FAV_TASKER_ANSWER_2": "To know the availability of your favorite Tasker, you can proactively contact and communicate directly with the Taskers through the Chat feature on the bTaskee app.", "HOW_TO_HAVE_FAV_TASKER_ANSWER_3": "In addition, the app displays the availability of the Tasker so you can proactively choose a suitable time slot. Unlike the usual way of booking individual tasks, you can also select and send up to three suitable time slots to the Tasker, allowing them to arrange their schedule and accept the job for you more conveniently.", "HOW_TO_HAVE_FAV_TASKER_ANSWER_4": "This feature helps you save time and ensures that you are always served by the person you trust and are most satisfied with.", "HOW_TO_REMOVE_FAV_TASKER": "How do I update the list of favorite Taskers?", "HOW_TO_REMOVE_FAV_TASKER_ANSWER": "Open the bTaskee app and select 'Account,' then tap on 'Favorite Taskers' to view your list of favorite Taskers. Here, you can add to or remove from your favorite list.", "ADD_FAVORITE_TASKER_TITLE": "Add favorite Tasker", "BTN_BACK": "Back", "CONFIRM": "Confirm", "MENU_FAVORITE_LIST": "Favorite Taskers", "NO_FAVOURITE_TASKER": "b<PERSON><PERSON><PERSON> encourages you to have multiple Taskers in your favorites list so that your tasks are always handled by people you are satisfied with.", "POST_TASK_NOW": "Post your task now", "SEE_FAV_TASKER_INTRODUCE": "More details", "NEW_LOGIN_FAV_TASKER": "When you rate a Tasker 5 stars, she/he will be added into your favorite Tasker list. You can choose to add or remove the Taskers in this list at any time. The system will send your next Task to your favorite Taskers first if they're available. We suggest you to try a few different Taskers once in a while to build up your favorite Tasker list. This will free you up from being dependant on only 1 Tasker.", "FAVORITE_TASKER_TAB": {"CHAT_WITH_TASKER": "Chat with <PERSON><PERSON>", "LAST_BOOKING": "The recent booking: {{t}}", "TITLE_MODAL_REMOVE_FAVORITE_TASKER": "Remove from favorite Tasker list", "CONFIRM_REMOVE": "Are you sure you want to remove this Tasker from your favorite Tasker list?", "ADD_FAVORITE_SUCCESS": "Added to favorite Tasker list", "REMOVE_FAVORITE_SUCCESS": "Removed from favorite Tasker list"}, "CONVERSATION": {"BOOK_NOW": "Book service now"}, "BLOCK_LIST_TASKER_TITLE": "These Taskers will not see your task", "TASKER_BLACK_LIST_EMPTY": "There is currently no tasker. Kindly post a task now to use the Block option. ", "TASKER_BLACK_LIST_EMPTY_1": "Block list is empty. Please add tasker to block list if they failed to meet your working standard. The blocked tasker will not be able to accept your task. ", "ADD_BLACK_LIST_TITLE": "List of Tasker", "DELETE": "Delete", "ADD_BLACK_LIST_SUCCESS": "Added to blacklist successfully", "ADD_BLACK_LIST_ERROR": "Failed to add to blacklist", "REMOVE_BLACK_LIST_SUCCESS": "Removed from blacklist successfully", "REMOVE_BLACK_LIST_ERROR": "Failed to remove from blacklist", "CONFIRM_REMOVE_BLACKLIST": "Are you sure you want to remove this tasker from blacklist?", "CANCEL": "Cancel"}