/*
 * @Filename: FavoriteTaskerEmpty/index.tsx
 * @Description: Empty state component for favorite tasker list
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, { type JSX } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  IObjectText,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { favoriteTasker } from '@images';

import { styles } from './styles';

interface FavoriteTaskerEmptyProps {
  isShow: boolean;
  onOpenIntro: () => void;
}

/**
 * Empty state component for favorite tasker list
 * Purpose: Shows when user has no favorite taskers with option to learn more
 * @param isShow - Whether to show the empty state component
 * @param onOpenIntro - Callback function to open introduction screen
 * @returns {JSX.Element | null} React component for empty state or null if not shown
 */
export const FavoriteTaskerEmpty = ({
  isShow,
  onOpenIntro,
}: FavoriteTaskerEmptyProps): JSX.Element | null => {
  const { t } = useI18n();

  if (!isShow) {
    return null;
  }

  return (
    <BlockView
      flex
      style={styles.wrapContainerNoData}
    >
      <BlockView
        center
        margin={{ bottom: Spacing.SPACE_32 }}
      >
        <FastImage
          style={styles.imageNoData}
          source={favoriteTasker}
          resizeMode="cover"
        />
        <CText
          bold
          size={FontSizes.SIZE_18}
          margin={{ vertical: Spacing.SPACE_16 }}
        >
          {t('MENU_FAVORITE_LIST' as keyof IObjectText)}
        </CText>
        <CText center>{t('NO_FAVOURITE_TASKER' as keyof IObjectText)}</CText>
      </BlockView>

      <TouchableOpacity onPress={onOpenIntro}>
        <BlockView style={styles.button}>
          <CText
            bold
            color={ColorsV2.green500}
            size={FontSizes.SIZE_12}
            padding={{ vertical: Spacing.SPACE_08 }}
          >
            {t('SEE_FAV_TASKER_INTRODUCE' as keyof IObjectText)}
          </CText>
        </BlockView>
      </TouchableOpacity>
    </BlockView>
  );
};
