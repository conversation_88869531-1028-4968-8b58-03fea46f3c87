import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  DeviceHelper,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  imageNoData: {
    width: Math.round(DeviceHelper.WINDOW.WIDTH / 3),
    height: Math.round(DeviceHelper.WINDOW.WIDTH / 3),
  },

  wrapContainerNoData: {
    paddingVertical: Spacing.SPACE_24,
    paddingHorizontal: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_16,
  },

  button: {
    backgroundColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
    paddingVertical: Spacing.SPACE_08,
    alignItems: 'center',
  },
});
