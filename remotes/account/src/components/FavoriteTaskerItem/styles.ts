import { StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  styleCard: {
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_08,
    backgroundColor: ColorsV2.neutralWhite,
  },

  containerItem: {
    justifyContent: 'space-between',
    flexDirection: 'row',
  },

  imageAvatar: {
    borderRadius: 60,
    width: 60,
    height: 60,
  },

  serviceText: {
    paddingTop: 7,
  },

  messageText: {
    paddingTop: 7,
  },

  boxContent: {
    paddingLeft: Spacing.SPACE_16,
    paddingRight: Spacing.SPACE_04,
  },

  boxBadge: {
    justifyContent: 'center',
  },

  btnRemove: {
    position: 'absolute',
    top: Spacing.SPACE_08,
    right: Spacing.SPACE_08,
    zIndex: 1,
  },
});
