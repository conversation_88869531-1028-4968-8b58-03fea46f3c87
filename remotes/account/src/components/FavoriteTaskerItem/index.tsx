/*
 * @Filename: FavoriteTaskerItem/index.tsx
 * @Description: Individual favorite tasker item component
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, { type JSX } from 'react';
import {
  Avatar,
  BlockView,
  Card,
  ColorsV2,
  ConditionView,
  CText,
  DateTimeHelpers,
  FastImage,
  FontSizes,
  HitSlop,
  IObjectText,
  IUser,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { iconRemoveCircleWM, iconStar } from '@images';

import { styles } from './styles';

interface FavoriteTaskerItemProps {
  tasker?: IUser;
  index?: number;
  onNavigateChat?: () => void;
  onRemoveFavoriteTasker?: () => void;
}

/**
 * Individual favorite tasker item component
 * Purpose: Displays a single favorite tasker with actions for removal and chat
 * @param tasker - Tasker data object containing user information
 * @param index - Index of the item in the list for tracking and testing
 * @param onNavigateChat - Callback function for navigating to chat with tasker
 * @param onRemoveFavoriteTasker - Callback function for removing tasker from favorites
 * @returns {JSX.Element} React component for favorite tasker item
 */
export const FavoriteTaskerItem = ({
  tasker,
  index,
  onRemoveFavoriteTasker,
  onNavigateChat,
}: FavoriteTaskerItemProps): JSX.Element => {
  const { t } = useI18n();

  return (
    <Card style={styles.styleCard}>
      <TouchableOpacity
        testID={`btnRemove${tasker?.name}`}
        activeOpacity={0.7}
        hitSlop={HitSlop.MEDIUM}
        style={styles.btnRemove}
        onPress={onRemoveFavoriteTasker}
      >
        <FastImage
          source={iconRemoveCircleWM}
          style={{ width: 24, height: 24 }}
          tintColor={ColorsV2.neutral300}
        />
      </TouchableOpacity>

      <TouchableOpacity
        testID={`boxChat_${index}`}
        activeOpacity={0.7}
        style={styles.containerItem}
        onPress={onNavigateChat}
      >
        <BlockView flex>
          <BlockView
            testID={`btnFavoriteTasker_${index}`}
            flex
            row
            horizontal
          >
            <Avatar
              avatar={tasker?.avatar || ''}
              size={56}
              isPremiumTasker={tasker?.isPremiumTasker}
            />
            <BlockView
              flex
              style={styles.boxContent}
            >
              <CText
                testID={`taskerName_${index}`}
                size={FontSizes.SIZE_14}
                color={ColorsV2.neutral900}
                bold
                numberOfLines={1}
              >
                {tasker?.name}
              </CText>
              <ConditionView
                condition={Boolean(tasker?.avgRating)}
                viewTrue={
                  <BlockView
                    row
                    horizontal
                    margin={{ top: Spacing.SPACE_04 }}
                  >
                    <FastImage
                      source={iconStar}
                      style={{ width: 16, height: 16 }}
                      tintColor={ColorsV2.yellow400}
                    />
                    <CText margin={{ left: Spacing.SPACE_04 }}>
                      {tasker?.avgRating}
                    </CText>
                  </BlockView>
                }
              />
            </BlockView>
          </BlockView>

          <BlockView
            row
            horizontal
            border={{ top: { width: 1, color: ColorsV2.neutral100 } }}
            padding={{ top: Spacing.SPACE_08 }}
            margin={{ top: Spacing.SPACE_08 }}
          >
            <BlockView flex>
              <ConditionView
                condition={Boolean(tasker?.lastPostedTask)}
                viewTrue={
                  <CText
                    size={FontSizes.SIZE_12}
                    color={ColorsV2.neutral300}
                    numberOfLines={2}
                  >
                    {t('FAVORITE_TASKER_TAB.LAST_BOOKING', {
                      t: DateTimeHelpers.formatToString({
                        date: tasker?.lastPostedTask,
                        typeFormat: TypeFormatDate.DateShort,
                      }),
                    })}
                  </CText>
                }
              />
            </BlockView>
            <BlockView
              radius={16}
              backgroundColor={ColorsV2.neutral100}
              padding={{
                horizontal: Spacing.SPACE_16,
                vertical: Spacing.SPACE_08,
              }}
            >
              <CText
                bold
                color={ColorsV2.green500}
                size={FontSizes.SIZE_12}
              >
                {t('CONVERSATION.BOOK_NOW')}
              </CText>
            </BlockView>
          </BlockView>
        </BlockView>
      </TouchableOpacity>
    </Card>
  );
};
