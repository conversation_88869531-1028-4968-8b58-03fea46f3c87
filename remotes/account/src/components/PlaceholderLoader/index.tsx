/*
 * @Filename: PlaceholderLoader/index.tsx
 * @Description: Loading placeholder component for favorite tasker list
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, { type JSX } from 'react';
import { BlockView, Card, SkeletonBox } from '@btaskee/design-system';

import { styles } from './styles';

interface HolderItemProps {
  borderBottom?: boolean;
}

/**
 * Individual skeleton item for loading state
 * Purpose: Shows skeleton structure of a favorite tasker item while loading
 * @param borderBottom - Whether to show bottom border
 * @returns {JSX.Element} React component for skeleton item
 */
const HolderItem = ({ borderBottom }: HolderItemProps): JSX.Element => {
  return (
    <BlockView
      row
      horizontal
      jBetween
      style={[styles.containerItem, borderBottom && styles.borderBottom]}
    >
      <SkeletonBox style={styles.avatar} />
      <BlockView
        vertical
        style={styles.boxNameRating}
      >
        <SkeletonBox
          width="80%"
          style={styles.txtNameAccount}
        />
        <SkeletonBox style={styles.wrapContentPlaceHolder} />
      </BlockView>
      <SkeletonBox style={styles.touchableRemoveItem} />
    </BlockView>
  );
};

/**
 * Loading placeholder component for favorite tasker list
 * Purpose: Shows skeleton loading state while fetching favorite taskers data
 * @returns {JSX.Element} React component for loading placeholder
 */
export const PlaceholderLoader = (): JSX.Element => {
  return (
    <Card style={styles.cardContainer}>
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem borderBottom={true} />
      <HolderItem />
    </Card>
  );
};
