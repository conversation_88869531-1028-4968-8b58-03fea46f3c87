import { StyleSheet } from 'react-native';
import { ColorsV2, <PERSON><PERSON>Helper, Spacing } from '@btaskee/design-system';

const WIDTH_IMAGE = DeviceHelper.WINDOW.WIDTH * 0.15;

export const styles = StyleSheet.create({
  wrapContentPlaceHolder: {
    width: '35%',
    marginTop: Spacing.SPACE_04,
  },

  avatar: {
    width: WIDTH_IMAGE,
    height: WIDTH_IMAGE,
    borderRadius: WIDTH_IMAGE / 2,
  },

  boxNameRating: {
    width: '70%',
    paddingHorizontal: Spacing.SPACE_16,
  },

  txtNameAccount: {
    height: 16,
    marginBottom: Spacing.SPACE_04,
  },

  touchableRemoveItem: {
    width: 24,
    height: 24,
    borderRadius: 12,
  },

  containerItem: {
    paddingVertical: Spacing.SPACE_08,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  borderBottom: {
    borderBottomWidth: 1,
    borderBottomColor: ColorsV2.neutral100,
  },

  cardContainer: {
    margin: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },
});
