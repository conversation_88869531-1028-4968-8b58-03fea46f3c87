import React, { memo } from 'react';
import { StyleSheet } from 'react-native';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  IconImage,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { icChevronRight } from '@images';

export interface MenuItemProps {
  icon: IconAssets;
  title: string;
  onPress: () => void;
}

const MenuItem = ({ icon, title, onPress }: MenuItemProps) => {
  const { t } = useI18n();

  return (
    <TouchableOpacity
      onPress={onPress}
      gap={Spacing.SPACE_08}
      backgroundColor={ColorsV2.neutralWhite}
      radius={BorderRadius.RADIUS_08}
      row
      style={styles.container}
    >
      <BlockView
        backgroundColor={ColorsV2.orange50}
        radius={BorderRadius.RADIUS_FULL}
        width={24}
        height={24}
        center
      >
        <IconImage
          source={icon}
          size={14}
        />
      </BlockView>
      <CText
        size={FontSizes.SIZE_14}
        lineHeight={20}
        flex={1}
        color={ColorsV2.neutral800}
      >
        {t(title)}
      </CText>
      <IconImage
        source={icChevronRight}
        size={16}
      />
    </TouchableOpacity>
  );
};

export default memo(MenuItem);

const styles = StyleSheet.create({
  container: {
    paddingVertical: Spacing.SPACE_08,
  },
});
