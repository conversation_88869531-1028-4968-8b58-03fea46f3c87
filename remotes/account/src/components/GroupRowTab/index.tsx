import React, { memo } from 'react';
import { ImageSourcePropType, ImageStyle, StyleProp } from 'react-native';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FontSizes,
  IconImage,
  openUrl,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import { useI18n } from '@src/hooks';

import { icChevronRight } from '@images';

export interface TabProps {
  id: number;
  title: string;
  icon: ImageSourcePropType;
  rightText?: string;
  link: string;
}
interface GroupRowTabProps {
  data: TabProps[];
  iconStyle?: StyleProp<ImageStyle>;
}

const GroupRowTab = ({ data, iconStyle }: GroupRowTabProps) => {
  const { t } = useI18n();

  return (
    <BlockView
      border={{ width: 1, color: ColorsV2.neutral50 }}
      radius={BorderRadius.RADIUS_08}
      padding={Spacing.SPACE_16}
      gap={Spacing.SPACE_16}
    >
      {data.map((item, index) => (
        <TouchableOpacity
          onPress={() => openUrl(item.link)}
          row
          key={item.id}
          horizontal
          gap={Spacing.SPACE_08}
          jBetween
          border={{
            top:
              index > 0
                ? { width: 1, color: ColorsV2.neutral50 }
                : {
                    width: 0,
                    color: ColorsV2.neutral50,
                  },
          }}
          padding={{ top: index > 0 ? Spacing.SPACE_16 : 0 }}
        >
          <BlockView
            horizontal
            gap={Spacing.SPACE_08}
            row
          >
            <IconImage
              source={item.icon}
              size={24}
              style={iconStyle}
            />
            <CText
              semiBold
              size={FontSizes.SIZE_14}
              color={ColorsV2.neutral600}
            >
              {t(item.title)}
            </CText>
          </BlockView>

          <BlockView
            row
            horizontal
            gap={Spacing.SPACE_08}
          >
            <CText
              size={FontSizes.SIZE_14}
              color={ColorsV2.orange500}
            >
              {item.rightText}
            </CText>
            <IconImage
              source={icChevronRight}
              size={16}
            />
          </BlockView>
        </TouchableOpacity>
      ))}
    </BlockView>
  );
};

export default memo(GroupRowTab);
