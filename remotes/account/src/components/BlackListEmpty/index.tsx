/**
 * @Filename: components/BlackListEmpty/index.tsx
 * @Description: Empty state component for blacklist screen
 * @CreatedAt: 16/10/2020
 * @Author: Hong<PERSON><PERSON>h
 * @UpdatedAt: 2/1/2021
 * @UpdatedBy: linhnh
 **/

import React, { type JSX } from 'react';
import type { IObjectText } from '@btaskee/design-system';
import { BlockView, ColorsV2, CText, FastImage } from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { taskerBlacklist } from '@images';

import { styles } from './styles';

interface BlackListEmptyProps {
  suggestBlackListData?: any[];
}

/**
 * Empty state component for blacklist screen
 * Purpose: Shows appropriate message when blacklist is empty
 * @param suggestBlackListData - Array of suggested taskers for blacklist (optional)
 * @returns {JSX.Element} React component displaying empty state
 */
export const BlackListEmpty = ({
  suggestBlackListData = [],
}: BlackListEmptyProps): JSX.Element => {
  const { t } = useI18n();

  return (
    <BlockView style={styles.emptyContainer}>
      <BlockView style={styles.emptyImage}>
        <FastImage
          style={styles.imageDataEmpty}
          source={taskerBlacklist}
          resizeMode="cover"
        />
        {suggestBlackListData.length === 0 ? (
          <CText
            testID="blackListEmpty"
            center
            style={styles.txtEmpty}
          >
            {t('TASKER_BLACK_LIST_EMPTY' as keyof IObjectText)}
          </CText>
        ) : (
          <CText
            testID="blackListEmpty"
            center
            style={styles.txtEmpty}
          >
            {t('TASKER_BLACK_LIST_EMPTY_1' as keyof IObjectText)}
          </CText>
        )}
      </BlockView>
    </BlockView>
  );
};
