import { Dimensions, StyleSheet } from 'react-native';
import { ColorsV2, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');

export const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'space-between',
  },

  emptyImage: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: Spacing.SPACE_24,
    paddingHorizontal: Spacing.SPACE_40,
    marginTop: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },

  imageDataEmpty: {
    width: width / 2.5,
    height: width / 2.5,
  },

  txtEmpty: {
    color: ColorsV2.neutral300,
    marginTop: Spacing.SPACE_16,
  },
});
