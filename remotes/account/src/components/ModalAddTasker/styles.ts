import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { height } = Dimensions.get('window');

export const styles = StyleSheet.create({
  container: {
    justifyContent: 'space-between',
    flexDirection: 'row',
    backgroundColor: ColorsV2.neutralWhite,
  },
  taskerInfo: {
    marginLeft: Spacing.SPACE_08,
  },
  taskerDetails: {
    marginLeft: Spacing.SPACE_16,
  },
  ratingContainer: {
    marginTop: Spacing.SPACE_04,
  },
  ratingText: {
    marginLeft: Spacing.SPACE_04,
  },
  scrollContent: {
    maxHeight: Math.round(height * 0.6),
  },
  contentContainerStyle: {
    paddingBottom: Spacing.SPACE_16,
    marginHorizontal: Spacing.SPACE_16,
  },
  checkboxContainer: {
    padding: 0,
    marginLeft: 0,
    marginRight: 0,
  },
  checkboxText: {
    marginLeft: 0,
    marginRight: 0,
  },
  separator: {
    marginVertical: Spacing.SPACE_16,
  },
  wrapContainer: {
    paddingHorizontal: 0,
    paddingBottom: 0,
  },
  headerContainer: {
    marginBottom: Spacing.SPACE_16,
  },
  modalHandle: {
    width: Spacing.SPACE_56 + Spacing.SPACE_04, // 60
    height: Spacing.SPACE_04,
    backgroundColor: ColorsV2.neutral300,
    borderRadius: BorderRadius.RADIUS_08,
    position: 'absolute',
    top: -Spacing.SPACE_12,
  },
  modalTitle: {
    marginVertical: Spacing.SPACE_20,
  },
  buttonContainer: {
    ...Shadows.SHADOW_3,
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_20,
  },
});
