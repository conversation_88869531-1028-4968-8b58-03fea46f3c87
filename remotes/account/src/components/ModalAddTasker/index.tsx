import React, {
  forwardRef,
  type JS<PERSON>,
  useCallback,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Dimensions, ScrollView } from 'react-native';
import {
  Avatar,
  BlockView,
  CheckBox,
  CModal,
  ColorsV2,
  ConditionView,
  CText,
  FontSizes,
  Icon,
  IUser,
  PrimaryButton,
  SizedBox,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

const { width } = Dimensions.get('window');
const WIDTH_IMAGE = Math.round(width / 7);

interface IModalAddTasker {
  onAddTasker: (taskerIds: string[]) => void;
  title?: string;
  allTaskerList?: IUser[];
}

interface ModalAddTaskerRef {
  open: () => void;
  close: () => void;
}

interface AddTaskerItemProps {
  tasker?: IUser;
  checked?: boolean;
  onPressCheckBox?: () => void;
  isLast?: boolean;
}

/**
 * AddTaskerItem component for displaying individual tasker selection
 * Purpose: Shows tasker info with checkbox for selection in modal
 * @param tasker - Tasker user data
 * @param checked - Whether tasker is selected
 * @param onPressCheckBox - Callback for checkbox press
 * @param settingSystem - System settings for rating display
 * @param isLast - Whether this is the last item (for separator)
 * @returns {JSX.Element} React component for tasker selection item
 */
const AddTaskerItem = ({
  tasker,
  checked,
  onPressCheckBox,
  isLast,
}: AddTaskerItemProps): JSX.Element | null => {
  if (!tasker) {
    return null;
  }

  const name = tasker.name || '';
  const avgRating = tasker.avgRating || 0; // Simplified rating logic

  return (
    <BlockView inset={'bottom'}>
      <TouchableOpacity
        testID={`checkBox${name}`}
        onPress={onPressCheckBox}
        style={styles.container}
      >
        <BlockView
          flex
          row
          center
          style={styles.taskerInfo}
        >
          <Avatar
            size={WIDTH_IMAGE}
            avatar={tasker.avatar}
            isPremiumTasker={tasker.isPremiumTasker}
          />

          <BlockView
            flex
            style={styles.taskerDetails}
          >
            <CText
              testID={`txtTaskerModal_${name}`}
              bold
              numberOfLines={2}
              size={FontSizes.SIZE_16}
            >
              {name}
            </CText>

            <ConditionView
              condition={!!avgRating}
              viewTrue={
                <BlockView
                  row
                  style={styles.ratingContainer}
                >
                  <Icon
                    name="icStar"
                    size={16}
                    color={ColorsV2.yellow400}
                  />
                  <CText
                    size={FontSizes.SIZE_12}
                    color={ColorsV2.neutral300}
                    style={styles.ratingText}
                  >
                    {avgRating}
                  </CText>
                </BlockView>
              }
            />
          </BlockView>
        </BlockView>

        <BlockView center>
          <CheckBox
            containerStyle={styles.checkboxContainer}
            textStyle={styles.checkboxText}
            onChecked={onPressCheckBox}
            checked={checked}
          />
        </BlockView>
      </TouchableOpacity>

      <ConditionView
        condition={!isLast}
        viewTrue={
          <SizedBox
            height={1}
            color={ColorsV2.neutral100}
            style={styles.separator}
          />
        }
      />
    </BlockView>
  );
};

interface ListTaskerProps {
  allTaskerList?: IUser[];
  setChooseTaskers: (taskers: IUser[]) => void;
  chooseTaskerArr: IUser[];
}

/**
 * ListTasker component for displaying scrollable list of taskers
 * Purpose: Shows all available taskers with selection functionality
 * @param allTaskerList - Array of all available taskers
 * @param setChooseTaskers - Function to update selected taskers
 * @param chooseTaskerArr - Currently selected taskers
 * @param settingSystem - System settings
 * @returns {JSX.Element} React component for tasker list
 */
const ListTasker = ({
  allTaskerList,
  setChooseTaskers,
  chooseTaskerArr,
}: ListTaskerProps): JSX.Element | null => {
  /**
   * Handles tasker selection/deselection
   * Purpose: Toggles tasker in selected array
   * @param indexChoose - Index of tasker in selected array (-1 if not selected)
   * @param tasker - Tasker to toggle
   * @returns {void} No return value, updates selection state
   */
  const onChooseTasker = useCallback(
    (indexChoose: number, tasker: IUser): void => {
      if (indexChoose !== -1) {
        const updatedTaskers = [...chooseTaskerArr];
        updatedTaskers.splice(indexChoose, 1);
        setChooseTaskers(updatedTaskers);
      } else {
        setChooseTaskers([...chooseTaskerArr, tasker]);
      }
    },
    [chooseTaskerArr, setChooseTaskers],
  );

  if (!allTaskerList) return null;

  return (
    <ScrollView
      style={styles.scrollContent}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={styles.contentContainerStyle}
    >
      {allTaskerList.map((tasker, index) => {
        const indexChoose = chooseTaskerArr.findIndex(
          (choose) => choose._id === tasker._id,
        );
        const isLast = index === allTaskerList.length - 1;

        return (
          <AddTaskerItem
            key={`itemFavorite_${index}`}
            checked={indexChoose !== -1}
            tasker={tasker}
            onPressCheckBox={() => onChooseTasker(indexChoose, tasker)}
            isLast={isLast}
          />
        );
      })}
    </ScrollView>
  );
};

/**
 * ModalAddTasker component for selecting taskers to add to favorites
 * Purpose: Provides modal interface for multi-selecting taskers from worked list
 * @param onAddTasker - Callback function with selected tasker IDs
 * @param title - Modal title text
 * @param allTaskerList - List of available taskers to choose from
 * @param settingSystem - System settings for display configuration
 * @returns {JSX.Element} React component for add tasker modal
 */
export const ModalAddTasker = forwardRef<ModalAddTaskerRef, IModalAddTasker>(
  ({ onAddTasker, title, allTaskerList }, ref): JSX.Element => {
    const { t } = useI18n();
    const modalRef = useRef<any>(null);
    const [chooseTaskers, setChooseTaskers] = useState<IUser[]>([]);

    const chooseTaskerArr = useMemo(() => [...chooseTaskers], [chooseTaskers]);

    /**
     * Closes the modal
     * Purpose: Handles modal close action
     * @returns {void} No return value, closes modal
     */
    const handleClose = useCallback((): void => {
      modalRef?.current?.close();
    }, []);

    /**
     * Opens the modal
     * Purpose: Handles modal open action
     * @returns {void} No return value, opens modal
     */
    const handleOpen = useCallback((): void => {
      modalRef?.current?.open();
    }, []);

    // Expose methods to parent component via ref
    useImperativeHandle(ref, () => ({
      open() {
        handleOpen();
      },
      close() {
        handleClose();
      },
    }));

    /**
     * Handles adding selected taskers
     * Purpose: Processes selected taskers and calls parent callback
     * @returns {void} No return value, calls onAddTasker with selected IDs
     */
    const addTasker = useCallback((): void => {
      handleClose();
      setTimeout(() => {
        if (chooseTaskerArr?.length > 0) {
          const taskerIds = chooseTaskerArr
            .map((tasker) => tasker._id)
            .filter(Boolean) as string[];
          onAddTasker?.(taskerIds);
          setChooseTaskers([]);
        }
      }, 300);
    }, [chooseTaskerArr, onAddTasker, handleClose]);

    const disableBtnAdd = !chooseTaskerArr || chooseTaskerArr.length === 0;

    /**
     * Handles modal close tracking
     * Purpose: Tracks user interaction for analytics
     * @returns {void} No return value, sends tracking data
     */
    const trackingAddFavoriteTasker = useCallback((): void => {
      // Mock tracking - replace with actual tracking implementation
      // console.log('Tracking add favorite tasker modal close');
    }, []);

    return (
      <CModal
        ref={modalRef}
        title={title}
        hideButtonClose
        contentContainerStyle={styles.wrapContainer}
        closeModalAction={trackingAddFavoriteTasker}
        onClose={trackingAddFavoriteTasker}
        isShowHeader={false}
      >
        <BlockView
          center
          style={styles.headerContainer}
        >
          <BlockView style={styles.modalHandle} />
          <CText
            bold
            center
            size={FontSizes.SIZE_20}
            style={styles.modalTitle}
          >
            {title}
          </CText>
        </BlockView>

        <ListTasker
          allTaskerList={allTaskerList}
          chooseTaskerArr={chooseTaskerArr}
          setChooseTaskers={setChooseTaskers}
        />

        <BlockView
          style={styles.buttonContainer}
          inset={'bottom'}
        >
          <PrimaryButton
            testID="btnAddTasker"
            title={t('ADD')}
            onPress={addTasker}
            disabled={disableBtnAdd}
          />
        </BlockView>
      </CModal>
    );
  },
);

ModalAddTasker.displayName = 'ModalAddTasker';
