import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    padding: Spacing.SPACE_16,
    justifyContent: 'space-between',
    flexDirection: 'row',
    marginBottom: Spacing.SPACE_08,
    borderRadius: BorderRadius.RADIUS_08,
    backgroundColor: ColorsV2.neutralWhite,
    borderWidth: 1,
    borderColor: ColorsV2.neutral100,
  },

  txtNameAccount: {
    fontSize: FontSizes.SIZE_16,
    color: ColorsV2.neutral900,
    marginLeft: Spacing.SPACE_08,
  },

  boxSubName: {
    marginTop: Spacing.SPACE_04,
  },

  txtRating: {
    marginLeft: Spacing.SPACE_04,
    color: ColorsV2.neutral300,
  },

  boxNameRating: {
    flex: 1,
    paddingHorizontal: Spacing.SPACE_04,
  },

  wrapTouchableRemove: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  touchableRemove: {
    backgroundColor: ColorsV2.neutral50,
    justifyContent: 'center',
    paddingHorizontal: Spacing.SPACE_16,
    paddingVertical: Spacing.SPACE_08,
    borderRadius: BorderRadius.RADIUS_08,
  },

  txtRemove: {
    color: ColorsV2.green500,
  },
});
