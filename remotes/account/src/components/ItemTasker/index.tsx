/**
 * @Filename: components/item-tasker.tsx
 * @Description: Displays individual tasker item with remove/delete action
 * @CreatedAt: 22/5/2020
 * @Author: Hong<PERSON><PERSON>h
 * @UpdatedAt: 4/1/2021
 * @UpdatedBy: linhnh, Duc<PERSON>nh
 **/

import React, { type JSX } from 'react';
import {
  Avatar,
  BlockView,
  ColorsV2,
  CText,
  Icon,
  IObjectText,
  IUser,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface ItemTaskerProps {
  onPressRightIcon?: () => void;
  onPressItem?: () => void;
  taskerDetail?: IUser;
  settingSystem?: any;
  taskerSettings?: any;
  iconName?: string; // Currently not used but kept for future extensibility
}

/**
 * Individual tasker item component with action button
 * Purpose: Displays tasker information with star rating and remove/delete action
 * @param onPressRightIcon - Callback function when right icon is pressed
 * @param onPressItem - Callback function when item is pressed
 * @param taskerDetail - Tasker data object containing name, rating, etc.
 * @param settingSystem - System settings for rating display
 * @param taskerSettings - Tasker-specific settings for avatar frame
 * @param iconName - Name of the icon to display (e.g., 'times' for remove)
 * @returns {JSX.Element} React component displaying tasker item
 */
export const ItemTasker = ({
  onPressRightIcon,
  taskerDetail,
  onPressItem,
  settingSystem,
  taskerSettings,
}: ItemTaskerProps): JSX.Element | null => {
  const { t } = useI18n();

  // Helper function to get average rating
  const getAvgRating = (
    tasker: any,
    numberOfTaskCanSeeRatingTasker: number = 0,
  ): string => {
    if (!tasker?.avgRating) return '0.0';

    const totalTask = tasker.totalTask || 0;
    if (totalTask < numberOfTaskCanSeeRatingTasker) {
      return 'N/A';
    }

    return parseFloat(tasker.avgRating).toFixed(1);
  };

  const avgRating = getAvgRating(
    taskerDetail,
    settingSystem?.numberOfTaskCanSeeRatingTasker,
  );

  if (!taskerDetail) {
    return null;
  }

  return (
    <TouchableOpacity
      disabled={!onPressItem}
      onPress={onPressItem}
      style={styles.container}
    >
      <BlockView
        row
        center
        flex
      >
        <Avatar
          avatar={taskerDetail?.avatar}
          size={60}
          isPremiumTasker={taskerDetail?.isPremiumTasker}
          avatarFrameSettings={taskerSettings?.avatarFrame}
        />

        <BlockView
          vertical
          flex
          style={styles.boxNameRating}
        >
          <CText
            testID={`ListTasker${taskerDetail.name}`}
            bold
            numberOfLines={2}
            style={styles.txtNameAccount}
          >
            {taskerDetail.name}
          </CText>
          {taskerDetail.avgRating ? (
            <BlockView
              row
              style={styles.boxSubName}
            >
              <Icon
                name="icStar"
                color={ColorsV2.orange500}
              />
              <CText
                testID={`avgRating${taskerDetail.name}`}
                style={styles.txtRating}
              >
                {avgRating}
              </CText>
            </BlockView>
          ) : null}
        </BlockView>
      </BlockView>

      <BlockView style={styles.wrapTouchableRemove}>
        <TouchableOpacity
          testID={`btnRemove${taskerDetail.name}`}
          onPress={onPressRightIcon}
          style={styles.touchableRemove}
        >
          <CText
            bold
            style={styles.txtRemove}
          >
            {t('DELETE')}
          </CText>
        </TouchableOpacity>
      </BlockView>
    </TouchableOpacity>
  );
};
