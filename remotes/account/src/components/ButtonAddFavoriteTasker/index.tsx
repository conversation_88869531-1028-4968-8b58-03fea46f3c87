/*
 * @Filename: ButtonAddFavoriteTasker/index.tsx
 * @Description: Button component for adding favorite taskers
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, { type JSX } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  IObjectText,
  IUser,
  PrimaryButton,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface ButtonAddFavoriteTaskerProps {
  onPress: () => void;
  isDisabled: boolean;
  user?: IUser;
  onNavigateHome?: () => void;
}

/**
 * Button component for adding favorite taskers
 * Purpose: Provides action to add new favorite taskers or navigate to post task
 * @param onPress - Callback function when add button is pressed
 * @param isDisabled - Whether the button is disabled (no worked taskers available)
 * @param user - Current user object to check task completion status
 * @param onNavigateHome - Callback function to navigate to home screen for posting tasks
 * @returns {JSX.Element} React component for add favorite tasker button
 */
export const ButtonAddFavoriteTasker = ({
  onPress,
  isDisabled,
  user,
  onNavigateHome,
}: ButtonAddFavoriteTaskerProps): JSX.Element => {
  const { t } = useI18n();

  // The user does not have taskDone, then switch to the post task screen
  if (isDisabled && !user?.taskDone) {
    return (
      <PrimaryButton
        testID={'FavTaskerBookTask'}
        title={t('POST_TASK_NOW')}
        onPress={onNavigateHome || (() => {})}
      />
    );
  }

  return (
    <BlockView style={styles.boxFooter}>
      <TouchableOpacity
        testID="btnAddFavTasker"
        onPress={onPress}
        disabled={isDisabled}
        style={[
          styles.btnAddFavTasker,
          isDisabled ? { backgroundColor: ColorsV2.neutral100 } : {},
        ]}
      >
        <CText
          bold
          style={styles.txtTitle}
        >
          {t('ADD_FAVORITE_TASKER_TITLE')}
        </CText>
      </TouchableOpacity>
    </BlockView>
  );
};
