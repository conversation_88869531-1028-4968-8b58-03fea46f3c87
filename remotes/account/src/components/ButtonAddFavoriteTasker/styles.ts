import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  FontSizes,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

export const styles = StyleSheet.create({
  boxFooter: {
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_16,
  },

  btnAddFavTasker: {
    backgroundColor: ColorsV2.green500,
    paddingVertical: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.SHADOW_2,
  },

  txtTitle: {
    color: ColorsV2.neutralWhite,
    fontSize: FontSizes.SIZE_16,
    fontWeight: 'bold',
  },
});
