import React, { Fragment, memo, useCallback } from 'react';
import {
  BlockView,
  ColorsV2,
  CText,
  FontSizes,
  IconAssets,
  NavigationService,
  RouteName,
  Spacing,
} from '@btaskee/design-system';

import MenuItem from '../MenuItem';

interface MenuProps {
  title: string;
  data: {
    id: number;
    icon: IconAssets;
    title: string;
    routeName: RouteName;
    routeNameInApp: string;
  }[];
}

const Menu = ({ data, title }: MenuProps) => {
  const renderAccountMenuItem = useCallback(
    (
      { routeName, routeNameInApp, ...item }: (typeof data)[0],
      index: number,
    ) => {
      return (
        <Fragment key={index}>
          {!!index && (
            <BlockView
              width={'100%'}
              height={1}
              backgroundColor={ColorsV2.neutral100}
            />
          )}

          <MenuItem
            {...item}
            onPress={() => {
              NavigationService.navigate(routeName, {
                screen: routeNameInApp,
              });
            }}
          />
        </Fragment>
      );
    },
    [],
  );

  return (
    <BlockView gap={Spacing.SPACE_12}>
      <CText
        size={FontSizes.SIZE_14}
        bold
        color={ColorsV2.neutral800}
      >
        {title}
      </CText>
      {data.map(renderAccountMenuItem)}
    </BlockView>
  );
};

export default memo(Menu);
