/*
 * @Filename: IntroFavoriteTaskerScreen/index.tsx
 * @Description: Introduction screen for favorite tasker functionality
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, { type JSX, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  AnimationHelpers,
  BlockView,
  ColorsV2,
  ConditionView,
  CText,
  FastImage,
  Icon,
  IObjectText,
  PrimaryButton,
  ScrollView,
  SizedBox,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';
import { favoriteTasker } from '@images';

import { styles } from './styles';

// Question data structure for FAQ section
const questionArr: Array<{
  title: string;
  content: string[];
}> = [
  {
    title: 'WHAT_IS_FAV_TASKERS',
    content: ['WHAT_IS_FAV_TASKERS_ANSWER'],
  },
  {
    title: 'WHY_DO_NEED_FAV_TASKERS',
    content: [
      'WHY_DO_NEED_FAV_TASKERS_ANSWER_1',
      'WHY_DO_NEED_FAV_TASKERS_ANSWER_2',
      'WHY_DO_NEED_FAV_TASKERS_ANSWER_3',
    ],
  },
  {
    title: 'HOW_TO_HAVE_FAV_TASKER',
    content: [
      'HOW_TO_HAVE_FAV_TASKER_ANSWER_1',
      'HOW_TO_HAVE_FAV_TASKER_ANSWER_2',
      'HOW_TO_HAVE_FAV_TASKER_ANSWER_3',
      'HOW_TO_HAVE_FAV_TASKER_ANSWER_4',
    ],
  },
  {
    title: 'HOW_TO_REMOVE_FAV_TASKER',
    content: ['HOW_TO_REMOVE_FAV_TASKER_ANSWER'],
  },
];

// Question item props interface
interface QuestionItemProps {
  question: { title: string; content: string[] };
  isShow: boolean;
  onPress: () => void;
}

/**
 * Individual question item component for FAQ section
 * Purpose: Displays expandable question and answer pairs
 * @param question - Object containing question title and content array
 * @param isShow - Boolean indicating if the answer should be shown
 * @param onPress - Callback function when question is pressed
 * @returns {JSX.Element} React component for question item
 */
const QuestionItem = ({
  question,
  isShow,
  onPress,
}: QuestionItemProps): JSX.Element => {
  const { t } = useI18n();

  return (
    <TouchableOpacity onPress={onPress}>
      <BlockView style={styles.groupContentItems}>
        <BlockView
          row
          jBetween
        >
          <CText
            flex
            bold
            color={isShow ? ColorsV2.orange500 : ColorsV2.neutral900}
          >
            {t(question.title as keyof IObjectText)}
          </CText>
          <Icon
            name={isShow ? 'icArrowUp' : 'icArrowDown'}
            color={ColorsV2.neutral900}
            size={18}
          />
        </BlockView>

        <ConditionView
          condition={isShow}
          viewTrue={
            <BlockView margin={{ top: Spacing.SPACE_16 }}>
              <SizedBox
                height={1}
                color={ColorsV2.neutral100}
              />
              {question.content?.map((item) => (
                <CText
                  key={item}
                  style={styles.txtContent}
                >
                  {t(item as keyof IObjectText)}
                </CText>
              ))}
            </BlockView>
          }
        />
      </BlockView>
    </TouchableOpacity>
  );
};

// Main component props interface
interface IntroFavoriteTaskerScreenProps {
  navigation?: {
    goBack: () => void;
  };
}

/**
 * Introduction screen for favorite tasker functionality
 * Purpose: Provides information about favorite taskers feature with FAQ section
 * @param navigation - Navigation object with goBack method
 * @returns {JSX.Element} React component for intro favorite tasker screen
 */
export const IntroFavoriteTaskerScreen = ({
  navigation,
}: IntroFavoriteTaskerScreenProps): JSX.Element => {
  const { t } = useI18n();
  const [isShow, setIsShow] = useState<number | null>(0);
  const insets = useSafeAreaInsets();

  /**
   * Handles the expand/collapse functionality for FAQ items
   * Purpose: Manages which FAQ section is currently expanded
   * @param index - Index of the question to toggle
   * @returns {void} No return value, updates component state
   */
  const handleShow = (index: number): void => {
    AnimationHelpers.runLayoutAnimation();
    if (index === isShow) {
      setIsShow(null);
      return;
    }
    setIsShow(index);
  };

  /**
   * Handles closing the screen and navigation back
   * Purpose: Provides a way to dismiss the screen
   * @returns {void} No return value, triggers navigation
   */
  const handleClose = (): void => {
    navigation?.goBack();
  };

  return (
    <BlockView
      flex
      inset={'bottom'}
      backgroundColor={ColorsV2.neutral10}
    >
      <ScrollView scrollIndicatorInsets={{ right: 1 }}>
        <BlockView>
          <FastImage
            style={styles.headerImage}
            resizeMode={'cover'}
            source={favoriteTasker}
          >
            <BlockView positionTop={insets.top}>
              <TouchableOpacity
                onPress={handleClose}
                style={styles.boxIconHeaderTop}
              >
                <Icon
                  name={'icBack'}
                  color={ColorsV2.neutral500}
                  style={styles.iconHeaderTop}
                />
              </TouchableOpacity>
            </BlockView>
          </FastImage>
        </BlockView>

        {/*========================= C O N T E N T =========================*/}
        <BlockView style={styles.content}>
          {questionArr.map((question, index) => (
            <QuestionItem
              key={index}
              question={question}
              isShow={Boolean(isShow === index)}
              onPress={() => handleShow(index)}
            />
          ))}
        </BlockView>
        {/*========================= E N D   C O N T E N T ==================*/}
      </ScrollView>

      <BlockView style={styles.boxButton}>
        <PrimaryButton
          onPress={handleClose}
          title={t('INTRODUCTION_BUTTON_UNDERSTAND' as keyof IObjectText)}
          style={styles.buttonStyle}
        />
      </BlockView>
    </BlockView>
  );
};
