import { StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ontSizes,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const HEADER_ICON_HEIGHT = DeviceHelper.WINDOW.WIDTH / 11;

export const styles = StyleSheet.create({
  headerImage: {
    width: DeviceHelper.WINDOW.WIDTH,
    height: <PERSON><PERSON><PERSON>elper.WINDOW.WIDTH / 2,
  },
  boxIconHeaderTop: {
    left: Spacing.SPACE_16,
    position: 'absolute',
    width: HEADER_ICON_HEIGHT,
    height: HEADER_ICON_HEIGHT,
    borderRadius: HEADER_ICON_HEIGHT / 2,
    justifyContent: 'center',
    alignItems: 'center',
  },

  iconHeaderTop: {
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
  },

  content: {
    paddingHorizontal: Spacing.SPACE_16,
  },

  groupContentItems: {
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_08,
    padding: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_16,
  },

  txtContent: {
    color: ColorsV2.neutral900,
    fontSize: FontSizes.SIZE_14,
    marginTop: Spacing.SPACE_16,
  },

  boxButton: {
    margin: Spacing.SPACE_16,
  },

  buttonStyle: {
    ...Shadows.SHADOW_2,
    shadowColor: ColorsV2.green500,
  },
});
