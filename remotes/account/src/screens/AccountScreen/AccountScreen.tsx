import '../../i18n';

import React from 'react';
import { SafeAreaView, StatusBar } from 'react-native';
import { useAuth } from '@btaskee/auth-store';
import {
  Avatar,
  BlockView,
  BorderRadius,
  Colors,
  ColorsV2,
  CText,
  FontSizes,
  Icon,
  IconAssets,
  IconImage,
  imgAvatarDefault,
  NavigationService,
  PrimaryButton,
  ScrollView,
  Spacing,
  TouchableOpacity,
  useUserStore,
} from '@btaskee/design-system';

import { Menu } from '@components';
import { icMessage } from '@images';

import useAccountScreen from './hook';
import styles from './styles';

const AccountScreen = () => {
  const { onPressChat, t, ACCOUNT_MENU, SUPPORT_MENU, UTILITY_MENU } =
    useAccountScreen();
  const { isAuthenticated, logout } = useAuth();
  const { user } = useUserStore();
  const renderButtonLogin = () => {
    return (
      <TouchableOpacity
        onPress={() => {
          isAuthenticated ? logout() : NavigationService.navigate('Auth');
        }}
      >
        <BlockView
          row
          horizontal
          alignSelf="flex-start"
          radius={BorderRadius.RADIUS_08}
          backgroundColor={ColorsV2.green500}
          padding={{
            vertical: Spacing.SPACE_04,
            horizontal: Spacing.SPACE_12,
          }}
          margin={{ top: Spacing.SPACE_04 }}
        >
          <CText
            bold
            color={ColorsV2.neutralWhite}
            size={FontSizes.SIZE_14}
          >
            {isAuthenticated ? 'Đăng xuất' : 'Đăng nhập'}
          </CText>
          <Icon
            name="icNext"
            size={20}
            color={Colors.WHITE}
          />
        </BlockView>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
    >
      <SafeAreaView style={styles.safeAreaView}>
        <StatusBar
          barStyle="dark-content"
          backgroundColor="white"
        />
        <BlockView
          row
          jBetween
          horizontal
          gap={Spacing.SPACE_08}
        >
          <CText
            size={FontSizes.SIZE_18}
            bold
            fontFamily="bold"
            color={ColorsV2.neutral800}
            lineHeight={24}
          >
            {t('TAB_ACCOUNT')}
          </CText>
          <TouchableOpacity onPress={onPressChat}>
            <IconImage
              source={icMessage}
              style={styles.iconMessage}
            />
          </TouchableOpacity>
        </BlockView>

        <BlockView
          row
          horizontal
          gap={Spacing.SPACE_08}
        >
          <Avatar
            avatar={isAuthenticated ? user?.avatar : ''}
            size={56}
          />
          <BlockView>
            <CText
              semiBold
              size={FontSizes.SIZE_18}
              lineHeight={24}
              color={ColorsV2.neutral800}
            >
              {isAuthenticated ? user?.name : ''}
            </CText>
            {renderButtonLogin()}
          </BlockView>
        </BlockView>

        <BlockView gap={Spacing.SPACE_24}>
          <Menu
            title={t('TAB_ACCOUNT')}
            data={ACCOUNT_MENU}
          />
          <Menu
            title={t('UTILITY')}
            data={UTILITY_MENU}
          />
          <Menu
            title={t('SUPPORT')}
            data={SUPPORT_MENU}
          />
        </BlockView>
      </SafeAreaView>
    </ScrollView>
  );
};

export default AccountScreen;
