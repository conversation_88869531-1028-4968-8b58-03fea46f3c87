import { useCallback } from 'react';
import { IconAssets, RouteName, useUserStore } from '@btaskee/design-system';
import { useAppNavigation, useI18n } from '@src/hooks';
import { RouteName as RouteNameInApp } from '@src/navigation/RouteName';

const ACCOUNT_MENU = [
  {
    id: 1,
    icon: IconAssets.icUser,
    title: 'PERSONAL_PROFILE',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
  {
    id: 2,
    icon: IconAssets.icLocation,
    title: 'SAVED_ADDRESSES',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.SavedAddress,
  },
  {
    id: 3,
    icon: IconAssets.icHistory,
    title: 'TRANSACTION_HISTORY',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
  {
    id: 4,
    icon: IconAssets.icGiftFill,
    title: 'MY_OFFER',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
  {
    id: 5,
    icon: IconAssets.icFavoriteTasker,
    title: 'FAVORITE_TASKER',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.FavoriteTaskerScreen,
  },
  {
    id: 6,
    icon: IconAssets.icJournalXFill,
    title: 'BLOCK_LIST',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.TaskerBlacklistScreen,
  },
  {
    id: 7,
    icon: IconAssets.icBuildingPeople,
    title: 'BUSINESS_PROFILE',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
];

const SUPPORT_MENU = [
  {
    id: 1,
    icon: IconAssets.icHelp,
    title: 'HELP',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.Help,
  },
  {
    id: 2,
    icon: IconAssets.icSetting,
    title: 'SETTING',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.Setting,
  },
  {
    id: 3,
    icon: IconAssets.icBee,
    title: 'ABOUT_BTASKEE',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
];
const UTILITY_MENU = [
  {
    id: 1,
    icon: IconAssets.icPay,
    title: 'B_PAY',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.BPay,
  },
  {
    id: 2,
    icon: IconAssets.icBrewards,
    title: 'B_REWARDS',
    routeName: RouteName.BReward,
    routeNameInApp: 'BReward/Home',
  },
  {
    id: 3,
    icon: IconAssets.icPromote,
    title: 'VOUCHER_PACKAGE',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
  {
    id: 4,
    icon: IconAssets.icStarts,
    title: 'PERSONAL_PROFILE',
    routeName: RouteName.TabAccountNavigator,
    routeNameInApp: RouteNameInApp.AboutbTaskee,
  },
];

const useAccountScreen = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { user } = useUserStore();

  const onPressChat = useCallback(() => {
    // navigation.navigate('Auth');
  }, [navigation]);

  return { onPressChat, t, user, ACCOUNT_MENU, SUPPORT_MENU, UTILITY_MENU };
};

export default useAccountScreen;
