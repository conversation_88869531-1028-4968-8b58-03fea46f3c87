import React from 'react';
import {
  BlockView,
  BorderRadius,
  ColorsV2,
  CText,
  FastImage,
  FontSizes,
  getVersionAppCode,
  getVersionAppName,
  IconImage,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import { isEmpty } from 'lodash-es';

import { GroupRowTab } from '@components';
import { bgLinearAbout, icBeeWhite } from '@images';

import useAboutbTaskee from './hook';
import styles from './styles';

export const AboutbTaskeeScreen = () => {
  const { ACTIONS, SOCIALS, t } = useAboutbTaskee();
  const versionApp = getVersionAppName();
  const versionCode = getVersionAppCode();

  return (
    <ScrollView
      contentContainerStyle={styles.contentContainerStyle}
      showsVerticalScrollIndicator={false}
    >
      <BlockView
        width="100%"
        height={204}
      >
        <FastImage
          source={bgLinearAbout}
          style={styles.bgLinearAbout}
          resizeMode="cover"
        />
        <BlockView
          horizontal
          padding={Spacing.SPACE_32}
          gap={Spacing.SPACE_16}
        >
          <BlockView
            center
            backgroundColor={ColorsV2.orange500}
            radius={BorderRadius.RADIUS_16}
            width={72}
            height={72}
          >
            <IconImage
              source={icBeeWhite}
              size={45}
            />
          </BlockView>
          <BlockView
            gap={Spacing.SPACE_04}
            center
          >
            <CText
              color={ColorsV2.orange500}
              semiBold
              size={FontSizes.SIZE_18}
            >
              {t('VERSION', { version: versionApp })}
            </CText>
            <CText style={styles.buildInfo}>
              {t('VERSION_CODE', { versionCode })}
            </CText>
          </BlockView>
        </BlockView>
      </BlockView>

      <BlockView
        style={styles.innerContainer}
        radius={BorderRadius.RADIUS_08}
        margin={{ horizontal: Spacing.SPACE_16 }}
        gap={Spacing.SPACE_12}
      >
        <GroupRowTab data={ACTIONS} />
        {!isEmpty(SOCIALS) && (
          <GroupRowTab
            data={SOCIALS}
            iconStyle={styles.iconSocial}
          />
        )}
      </BlockView>
    </ScrollView>
  );
};

export default AboutbTaskeeScreen;
