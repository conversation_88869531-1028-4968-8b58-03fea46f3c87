import { useMemo } from 'react';
import { Platform } from 'react-native';
import {
  ISO_CODE,
  SOCIAL_BTASKEE_LINK,
  useAppStore,
  useSettingsStore,
} from '@btaskee/design-system';
import {
  URL_RATE_APP_ANDROID,
  URL_RATE_APP_IOS,
  WEBSITE_BTASKEE_URL,
} from '@src/constant';
import { useAppNavigation, useI18n } from '@src/hooks';
import { get } from 'lodash-es';

import {
  icEarth,
  icFacebook,
  icHelp,
  icInstagram,
  icLike,
  icLine,
  icShield,
  icYoutube,
  icZalo,
} from '@images';

const useAboutbTaskee = () => {
  const { t } = useI18n();
  const navigation = useAppNavigation();
  const { isoCode } = useAppStore();
  const { settings } = useSettingsStore();

  const SOCIALS = useMemo(() => {
    if (isoCode === 'ID') {
      return [];
    }

    const fbLinkDefault = get(SOCIAL_BTASKEE_LINK, `FACEBOOK.${isoCode}`, null);
    const youtubLinkDefault = get(
      SOCIAL_BTASKEE_LINK,
      `YOUTUBE.${isoCode}`,
      null,
    );
    const instagramLinkDefault = get(
      SOCIAL_BTASKEE_LINK,
      `INSTAGRAM.${isoCode}`,
      null,
    );
    const lineLinkDefault = get(SOCIAL_BTASKEE_LINK, `LINE.${isoCode}`, null);
    const zaloLinkDefault = get(SOCIAL_BTASKEE_LINK, `ZALO.${isoCode}`, null);

    const socials = [
      {
        id: 1,
        icon: icFacebook,
        title: 'Facebook',
        link: get(
          settings,
          'settingSystem.communityGroup.facebook',
          fbLinkDefault,
        ),
      },
      {
        id: 2,
        icon: icYoutube,
        title: 'Youtube',
        link: get(
          settings,
          'settingSystem.communityGroup.youtube',
          youtubLinkDefault,
        ),
      },
      {
        id: 3,
        icon: icInstagram,
        title: 'Instagram',
        link: get(
          settings,
          'settingSystem.communityGroup.instagram',
          instagramLinkDefault,
        ),
      },
      {
        id: 4,
        icon: icLine,
        title: 'Line',
        link: get(
          settings,
          'settingSystem.communityGroup.line',
          lineLinkDefault,
        ),
      },
      {
        icon: icZalo,
        id: 5,
        title: 'Zalo',
        link: get(
          settings,
          'settingSystem.communityGroup.zalo',
          zaloLinkDefault,
        ),
      },
    ];
    return socials.filter((item) => item.link);
  }, [settings, isoCode]);

  const ACTIONS = useMemo(
    () => [
      {
        id: 1,
        title: 'RATE_BTASKEE_NOW',
        icon: icLike,
        link: Platform.select({
          ios: URL_RATE_APP_IOS,
          android: URL_RATE_APP_ANDROID,
        }) as string,
      },
      {
        id: 2,
        title: 'Website',
        icon: icEarth,
        rightText: 'btaskee.com',
        link: WEBSITE_BTASKEE_URL[isoCode ?? ISO_CODE.VN],
      },
      {
        id: 3,
        title: 'TERMS_OF_USE',
        icon: icHelp,
        link: '2',
      },
      {
        id: 4,
        title: 'PRIVACY_POLICY',
        icon: icShield,
        link: '3',
      },
    ],
    [isoCode],
  );

  return { navigation, t, ACTIONS, SOCIALS };
};

export default useAboutbTaskee;
