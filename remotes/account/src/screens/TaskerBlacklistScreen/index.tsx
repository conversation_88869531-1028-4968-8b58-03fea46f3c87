/**
 * @Filename: screens/TaskerBlacklistScreen/index.tsx
 * @Description: Tasker blacklist management screen
 * @CreatedAt: 16/10/2020
 * @Author: Hong<PERSON><PERSON>h
 * @UpdatedAt: 2/1/2021
 * @UpdatedBy: linhnh
 **/

import React, {
  createRef,
  type JSX,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { RefreshControl } from 'react-native';
import {
  Alert,
  BlockView,
  CText,
  EndpointKeys,
  FlatList,
  FontSizes,
  IUser,
  PrimaryButton,
  ToastHelpers,
  useApiMutation,
  useAppLoading,
} from '@btaskee/design-system';

import {
  BlackListEmpty,
  ItemTasker,
  ModalAddTasker,
  PlaceholderLoader,
} from '@components';
import { useI18n } from '@hooks';

import { styles } from './styles';

interface TaskerBlacklistScreenProps {
  // Mock props - in real implementation these would come from Redux/Context
}

/**
 * Tasker blacklist management screen component
 * Purpose: Allows users to manage their blacklisted taskers - view, add, and remove
 * @param user - User data object (optional, mocked for now)
 * @param settingSystem - System settings for tasker display (optional, mocked for now)
 * @returns {JSX.Element} React component for managing blacklisted taskers
 */
export const TaskerBlacklistScreen =
  ({}: TaskerBlacklistScreenProps): JSX.Element => {
    const { t } = useI18n();
    const { showAppLoading, hideAppLoading } = useAppLoading();

    // State management
    const [blackListData, setBlackListData] = useState<IUser[]>([]);
    const [suggestBlackListData, setSuggestBlackListData] = useState<IUser[]>(
      [],
    );
    const [allTaskers, setAllTaskers] = useState<IUser[]>([]);
    const [disabledAdd, setDisabledAdd] = useState<boolean>(true);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [refreshing, setRefreshing] = useState<boolean>(false);

    // Modal ref for adding taskers to blacklist
    const addBlockListRef = createRef<{
      open: () => void;
      close: () => void;
    }>();

    // API Mutations using useApiMutation
    const { mutate: getBlackListTaskerAPI } = useApiMutation({
      key: EndpointKeys.getBlackListTasker,
      options: {
        onMutate: () => {},
        onSettled: () => {
          setIsLoading(false);
        },
        onSuccess: (data) => {
          setBlackListData((data as IUser[]) || []);
        },
        onError: () => {
          setBlackListData([]);
        },
      },
    });

    const { mutate: getSuggestBlackListAPI } = useApiMutation({
      key: EndpointKeys.getSuggestBlackList,
      options: {
        onSuccess: (data) => {
          setSuggestBlackListData((data as IUser[]) || []);
          setAllTaskers((data as IUser[]) || []);
          setDisabledAdd((data as IUser[])?.length === 0);
        },
        onError: () => {
          setSuggestBlackListData([]);
          setAllTaskers([]);
          setDisabledAdd(true);
        },
      },
    });

    const { mutate: addBlackListTaskerAPI } = useApiMutation({
      key: EndpointKeys.addBlackListTasker,
      options: {
        onMutate: () => {
          showAppLoading();
        },
        onSettled: () => {
          hideAppLoading();
        },
        onSuccess: () => {
          getBlackListTaskerAPI({});
          getSuggestBlackListAPI({});
          ToastHelpers.showSuccess({
            message: t('ADD_BLACK_LIST_SUCCESS'),
            position: 'top',
          });
        },
        onError: () => {
          ToastHelpers.showError({
            message: t('ADD_BLACK_LIST_ERROR'),
            position: 'top',
          });
        },
      },
    });

    const { mutate: removeBlackListTaskerAPI } = useApiMutation({
      key: EndpointKeys.removeBlackListTasker,
      options: {
        onMutate: () => {
          showAppLoading();
        },
        onSettled: () => {
          hideAppLoading();
        },
        onSuccess: () => {
          getBlackListTaskerAPI({});
          getSuggestBlackListAPI({});
          ToastHelpers.showSuccess({
            message: t('REMOVE_BLACK_LIST_SUCCESS'),
            position: 'top',
          });
        },
        onError: () => {
          ToastHelpers.showError({
            message: t('REMOVE_BLACK_LIST_ERROR'),
            position: 'top',
          });
        },
      },
    });

    // Load blacklist and suggestions on component mount
    useEffect(() => {
      getBlackListTaskerAPI({});
      getSuggestBlackListAPI({});
    }, [getBlackListTaskerAPI, getSuggestBlackListAPI]);

    /**
     * Handles pull-to-refresh functionality
     * Purpose: Refreshes the favorite taskers list
     * @returns {Promise<void>} No return value, updates list data
     */
    const onRefresh = (): void => {
      setRefreshing(true);
      getBlackListTaskerAPI({});
      // Use timeout to ensure refreshing state is visible
      setTimeout(() => setRefreshing(false), 1000);
    };

    /**
     * Opens modal to add taskers to blacklist
     * Purpose: Shows modal with suggested taskers that can be added to blacklist
     * @returns {void}
     */
    const openModalAddBlackList = useCallback((): void => {
      getSuggestBlackListAPI({}); // Refetch newest tasker list
      addBlockListRef?.current?.open();
    }, [getSuggestBlackListAPI, addBlockListRef]);

    /**
     * Adds selected taskers to blacklist
     * Purpose: Processes array of selected tasker IDs and adds them to blacklist
     * @param taskerIds - Array of tasker IDs to add to blacklist
     * @returns {void}
     */
    const addTaskerToBlackList = useCallback(
      (taskerIds: string[]): void => {
        if (taskerIds.length > 0) {
          addBlackListTaskerAPI({ taskerIds });
        }
        addBlockListRef?.current?.close();
      },
      [addBlackListTaskerAPI],
    );

    /**
     * Removes tasker from blacklist with confirmation
     * Purpose: Shows confirmation dialog before removing tasker from blacklist
     * @param taskerId - ID of the tasker to remove from blacklist
     * @returns {void}
     */
    const removeTasker = useCallback(
      (taskerId?: string): void => {
        Alert.alert.open({
          title: t('CONFIRM'),
          message: t('CONFIRM_REMOVE_BLACKLIST'),
          actions: [
            {
              text: t('CANCEL'),
              style: 'cancel',
            },
            {
              text: t('DELETE'),
              onPress: () => {
                removeBlackListTaskerAPI({ taskerIds: [taskerId] });
              },
            },
          ],
        });
      },
      [removeBlackListTaskerAPI, t],
    );

    const renderItem = ({
      item,
      index,
    }: {
      item: IUser;
      index: number;
    }): JSX.Element => (
      <ItemTasker
        taskerDetail={item}
        iconName="times"
        key={`itemBlacklist_${index}`}
        onPressRightIcon={() => removeTasker(item?._id)}
      />
    );

    const renderHeader = (): JSX.Element => (
      <BlockView style={styles.boxHeaderTitle}>
        <CText style={styles.txtAddFav}>{t('BLOCK_LIST_TASKER_TITLE')}</CText>
      </BlockView>
    );

    // Footer button for adding taskers (only show if suggestions available)
    const footerButton = useMemo(() => {
      if (disabledAdd) return null;

      return (
        <BlockView style={styles.boxFooter}>
          <PrimaryButton
            testID="btnAddBlackList"
            title={t('ADD_BLACK_LIST_TITLE')}
            onPress={openModalAddBlackList}
            style={styles.btnAddBlackList}
          />
        </BlockView>
      );
    }, [disabledAdd, openModalAddBlackList, t]);

    if (isLoading) return <PlaceholderLoader />;

    return (
      <BlockView style={styles.container}>
        <FlatList
          contentContainerStyle={styles.contentContainerStyle}
          data={blackListData}
          renderItem={renderItem}
          keyExtractor={(item: IUser, index: number) => `${item._id}-${index}`}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }
          ListEmptyComponent={
            <BlackListEmpty suggestBlackListData={suggestBlackListData} />
          }
          ListHeaderComponent={renderHeader}
          scrollIndicatorInsets={{ right: 1 }}
        />

        {footerButton}

        <ModalAddTasker
          title={t('ADD_BLACK_LIST_TITLE')}
          allTaskerList={allTaskers}
          ref={addBlockListRef}
          onAddTasker={addTaskerToBlackList}
        />
      </BlockView>
    );
  };
