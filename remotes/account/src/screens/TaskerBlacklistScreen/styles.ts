import { StyleSheet } from 'react-native';
import { BorderRadius, ColorsV2, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralBackground,
    justifyContent: 'space-between',
  },

  contentContainer: {
    paddingBottom: 0,
    flex: 1,
  },

  boxFooter: {
    backgroundColor: ColorsV2.neutralWhite,
    paddingVertical: Spacing.SPACE_24,
    paddingHorizontal: Spacing.SPACE_16,
  },

  btnAddBlackList: {
    paddingVertical: Spacing.SPACE_16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.RADIUS_08,
  },
  boxHeaderTitle: {
    marginTop: Spacing.SPACE_24,
    marginBottom: Spacing.SPACE_24,
  },

  txtAddFav: {
    marginLeft: Spacing.SPACE_04,
    color: ColorsV2.neutral300,
  },
  contentContainerStyle: {
    paddingTop: Spacing.SPACE_16,
    paddingHorizontal: Spacing.SPACE_16,
  },
});
