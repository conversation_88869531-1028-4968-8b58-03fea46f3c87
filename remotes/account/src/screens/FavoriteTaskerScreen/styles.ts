import { Dimensions, StyleSheet } from 'react-native';
import {
  BorderRadius,
  ColorsV2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  FontSizes,
  Shadows,
  Spacing,
} from '@btaskee/design-system';

const { width } = Dimensions.get('window');

const WIDTH_ICON = width * 0.06;

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
    justifyContent: 'space-between',
  },

  cardStyle: {
    backgroundColor: ColorsV2.neutralWhite,
  },

  boxHeaderTitle: {
    marginTop: Spacing.SPACE_32,
    marginBottom: Spacing.SPACE_24,
  },

  scrollContent: {
    padding: Spacing.SPACE_16,
  },

  boxAddFav: {
    flex: 1,
    flexDirection: 'row',
  },

  txtAddFav: {
    marginLeft: Spacing.SPACE_04,
    color: ColorsV2.green500,
  },

  txtRating: {
    marginLeft: Spacing.SPACE_08,
    color: ColorsV2.neutral300,
  },

  touchableRemove: {
    backgroundColor: ColorsV2.neutral300,
    justifyContent: 'center',
    width: WIDTH_ICON,
    height: WIDTH_ICON,
    borderRadius: WIDTH_ICON / 2,
    alignItems: 'center',
  },

  containerItem: {
    paddingVertical: Spacing.SPACE_08,
  },

  txtNameAccountItem: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutral900,
  },

  boxSubName: {
    marginTop: Spacing.SPACE_08,
  },

  // MODAL INTRO FAVORITE TASKER
  containerIntroFav: {
    padding: Spacing.SPACE_16,
  },

  txtTitle: {
    fontSize: FontSizes.SIZE_14,
    color: ColorsV2.neutralWhite,
    marginLeft: Spacing.SPACE_08,
  },

  wrapBtnFavTasker: {
    justifyContent: 'center',
    alignItems: 'center',
    width: Spacing.SPACE_24,
    height: Spacing.SPACE_24,
    borderRadius: Spacing.SPACE_24 / 2,
    backgroundColor: ColorsV2.neutralWhite,
  },

  imageNoData: {
    width: width / 2.5,
    height: width / 2.5,
    marginBottom: Spacing.SPACE_08,
  },

  wrapContainerNoData: {
    paddingVertical: Spacing.SPACE_24,
    paddingHorizontal: Spacing.SPACE_32,
    marginTop: Spacing.SPACE_16,
    backgroundColor: ColorsV2.neutralWhite,
  },

  contentContainerStyle: {
    paddingTop: Spacing.SPACE_16,
    paddingHorizontal: Spacing.SPACE_16,
  },

  // FAVORITE ITEM STYLES
  styleCard: {
    backgroundColor: ColorsV2.neutralWhite,
  },

  btnRemove: {
    position: 'absolute',
    top: Spacing.SPACE_08,
    right: Spacing.SPACE_08,
    zIndex: 1,
  },

  boxContent: {
    marginLeft: Spacing.SPACE_16,
  },

  // BUTTON ADD FAVORITE TASKER STYLES
  boxFooter: {
    backgroundColor: ColorsV2.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
    paddingTop: Spacing.SPACE_16,
  },

  btnAddFavTasker: {
    backgroundColor: ColorsV2.orange500,
    paddingVertical: Spacing.SPACE_16,
    borderRadius: BorderRadius.RADIUS_08,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.SHADOW_2,
  },

  txtTitleButton: {
    color: ColorsV2.neutralWhite,
    fontSize: FontSizes.SIZE_16,
    fontWeight: 'bold',
  },

  // EMPTY STATE STYLES
  emptyContainer: {
    flex: 1,
    backgroundColor: ColorsV2.neutralWhite,
    borderRadius: BorderRadius.RADIUS_16,
    paddingVertical: Spacing.SPACE_24,
    paddingHorizontal: Spacing.SPACE_32,
    marginTop: Spacing.SPACE_16,
    alignItems: 'center',
  },

  emptyImage: {
    width: width / 2.5,
    height: width / 2.5,
    marginBottom: Spacing.SPACE_08,
  },

  emptyTitle: {
    fontSize: FontSizes.SIZE_18,
    fontWeight: 'bold',
    marginVertical: Spacing.SPACE_16,
    textAlign: 'center',
  },

  emptyDescription: {
    textAlign: 'center',
    color: ColorsV2.neutral700,
    marginBottom: Spacing.SPACE_24,
  },

  emptyButton: {
    backgroundColor: ColorsV2.neutral100,
    borderRadius: BorderRadius.RADIUS_08,
    paddingVertical: Spacing.SPACE_08,
    paddingHorizontal: Spacing.SPACE_16,
  },

  emptyButtonText: {
    color: ColorsV2.green500,
    fontSize: FontSizes.SIZE_12,
    fontWeight: 'bold',
  },
});
