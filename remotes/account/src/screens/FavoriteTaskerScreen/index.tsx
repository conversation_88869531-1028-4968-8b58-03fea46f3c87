/*
 * @Filename: FavoriteTaskerScreen/index.tsx
 * @Description: Favorite tasker screen migrated from legacy project
 * @CreatedAt: Migrated from legacy project
 * @Author: System Migration
 * @UpdatedAt: Current migration
 * @UpdatedBy: Design System Migration
 */

import React, {
  createRef,
  type JSX,
  useCallback,
  useEffect,
  useState,
} from 'react';
import { RefreshControl } from 'react-native';
import {
  Alert,
  BlockView,
  CText,
  EndpointKeys,
  FlatList,
  IObjectText,
  IUser,
  NavigationService,
  PrimaryButton,
  RouteName as RouteNameSystem,
  SizedBox,
  Spacing,
  ToastHelpers,
  useApiMutation,
  useAppLoading,
  useUserStore,
} from '@btaskee/design-system';
import { RouteName } from '@src/navigation/RouteName';

import {
  ButtonAddFavoriteTasker,
  FavoriteTaskerEmpty,
  FavoriteTaskerItem,
  ModalAddTasker,
  PlaceholderLoader,
} from '@components';
import { useI18n } from '@hooks';

import { styles } from './styles';

const MAX_SHOW_DETAIL = 5;

// Mock user data - replace with real user from context/redux
const mockUser = { _id: 'user123', taskDone: 5 };

// Props interfaces

interface FavoriteTaskerScreenProps {
  route?: {
    params?: {
      entryPoint?: string;
    };
  };
}

// Components are now imported from @components - no need to define them here

// PlaceholderLoader is now imported from @components

/**
 * Not logged in component
 * Purpose: Shows login encouragement for unauthenticated users
 * @returns {JSX.Element} React component for not logged in state
 */
const NotLoggedIn = (): JSX.Element => {
  const { t } = useI18n();

  return (
    <BlockView
      flex
      center
      padding={Spacing.SPACE_24}
    >
      <CText
        center
        margin={{ bottom: Spacing.SPACE_16 }}
      >
        {t('NEW_LOGIN_FAV_TASKER')}
      </CText>
      <PrimaryButton
        title="Login"
        onPress={() => {
          // Navigate to login
        }}
      />
    </BlockView>
  );
};

/**
 * Main favorite tasker screen component
 * Purpose: Displays and manages list of favorite taskers with CRUD operations
 * @param navigation - Navigation object for screen transitions
 * @param route - Route object containing navigation parameters
 * @returns {JSX.Element} React component for favorite tasker screen
 */
export const FavoriteTaskerScreen = ({
  route,
}: FavoriteTaskerScreenProps): JSX.Element => {
  const { t } = useI18n();

  const { user } = useUserStore();
  const [favoriteTaskers, setFavoriteList] = useState<IUser[]>([]);
  const [allTaskers, setAllTaskers] = useState<IUser[]>([]);
  const [disabledAdd, setDisabledAdd] = useState(false);
  const [isGetApi, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const { showAppLoading, hideAppLoading } = useAppLoading();

  // Modal ref for add favorite tasker
  const modalAddFavorite = createRef<{ open: () => void; close: () => void }>();

  // API Mutations using useApiMutation
  const { mutate: getFavoriteTaskerAPI } = useApiMutation({
    key: EndpointKeys.getFavoriteTasker,
    options: {
      onMutate: () => {},
      onSettled: () => {
        setLoading(false);
      },
      onSuccess: (data) => {
        setFavoriteList((data as IUser[]) || []);
      },
      onError: () => {},
    },
  });

  const { mutate: getListTaskerWorkedAPI } = useApiMutation({
    key: EndpointKeys.getListTaskerWorked,
    options: {
      onSuccess: (data) => {
        setAllTaskers((data as IUser[]) || []);
        setDisabledAdd((data as IUser[])?.length === 0);
      },
      onError: () => {},
    },
  });

  const { mutate: addFavoriteTaskerAPI } = useApiMutation({
    key: EndpointKeys.addFavoriteTasker,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
      onSuccess: () => {
        // Refresh favorite taskers list
        getFavoriteTaskerAPI({});
        ToastHelpers.showSuccess({
          message: t('FAVORITE_TASKER_TAB.ADD_FAVORITE_SUCCESS'),
          position: 'top',
        });
      },
      onError: () => {},
    },
  });

  const { mutate: removeFavoriteTaskerAPI } = useApiMutation({
    key: EndpointKeys.removeFavoriteTasker,
    options: {
      onMutate: () => {
        showAppLoading();
      },
      onSettled: () => {
        hideAppLoading();
      },
      onSuccess: () => {
        ToastHelpers.showSuccess({
          message: t('FAVORITE_TASKER_TAB.REMOVE_FAVORITE_SUCCESS'),
          position: 'top',
        });
      },
      onError: () => {},
    },
  });

  /**
   * Fetches favorite taskers from API
   * Purpose: Retrieves user's favorite tasker list
   * @param isShowShadowLoading - Whether to show loading indicator
   * @returns {Promise<void>} No return value, updates component state
   */
  const getFavoriteTasker = useCallback(
    (isShowShadowLoading?: boolean): void => {
      if (isShowShadowLoading) {
        setLoading(true);
      }
      getFavoriteTaskerAPI({});
    },
    [getFavoriteTaskerAPI],
  );

  /**
   * Fetches list of all taskers user has worked with
   * Purpose: Gets available taskers for adding to favorites
   * @returns {Promise<void>} No return value, updates component state
   */
  const getListTaskerWorked = useCallback((): void => {
    getListTaskerWorkedAPI({});
  }, [getListTaskerWorkedAPI]);

  useEffect(() => {
    // Mock tracking - replace with actual tracking implementation
    // trackingScreenView({ screenName: 'FavoriteTasker', entryPoint: route?.params?.entryPoint });
  }, [route?.params?.entryPoint]);

  useEffect(() => {
    if (mockUser) {
      getFavoriteTasker(true);
      getListTaskerWorked();
    }
  }, [getFavoriteTasker, getListTaskerWorked]);

  useEffect(() => {
    setDisabledAdd(Boolean(!allTaskers || allTaskers?.length === 0));
  }, [allTaskers]);

  /**
   * Adds a tasker to favorites list
   * Purpose: Handles adding selected tasker to user's favorites
   * @param taskerIds - ID(s) of tasker(s) to add
   * @returns {void} No return value, uses mutation to handle success
   */
  const addTaskerToFavorite = (taskerIds?: string | string[]): void => {
    addFavoriteTaskerAPI({ taskerIds });
  };

  /**
   * Opens modal to add favorite tasker
   * Purpose: Shows interface for selecting and adding favorite taskers
   * @returns {void} No return value, opens modal
   */
  const openModalAddFavorite = (): void => {
    getListTaskerWorked(); // Refetch newest Tasker list
    modalAddFavorite?.current?.open && modalAddFavorite?.current?.open();
  };

  /**
   * Removes tasker from favorites list (optimistic update)
   * Purpose: Immediately updates UI before API call
   * @param taskerId - ID of tasker to remove
   * @returns {void} No return value, updates component state
   */
  const removeFavoriteTasker = (taskerId?: string): void => {
    const newFavList = favoriteTaskers.filter(
      (tasker) => tasker._id !== taskerId,
    );
    setFavoriteList(newFavList);
  };

  /**
   * Removes tasker from favorites via API
   * Purpose: Handles server-side removal of favorite tasker
   * @param taskerId - ID of tasker to remove
   * @returns {Promise<void>} No return value, shows success message
   */
  const removeTasker = (taskerId?: string): void => {
    if (taskerId) {
      // Optimistic update - remove from local state immediately
      removeFavoriteTasker(taskerId);
      setDisabledAdd(false);

      // Call API to remove from server
      removeFavoriteTaskerAPI({ taskerIds: [taskerId] });
    }
  };

  /**
   * Shows confirmation dialog for removing favorite tasker
   * Purpose: Confirms user intent before removing tasker from favorites
   * @param taskerId - ID of tasker to remove
   * @returns {void} No return value, shows alert dialog
   */
  const handleRemoveFavoriteTasker = (taskerId?: string): void => {
    Alert.alert.open({
      title: t('FAVORITE_TASKER_TAB.TITLE_MODAL_REMOVE_FAVORITE_TASKER'),
      message: t('FAVORITE_TASKER_TAB.CONFIRM_REMOVE'),
      actions: [
        {
          text: t('BTN_BACK'),
          style: 'cancel',
        },
        {
          text: t('CONFIRM'),
          onPress: () => {
            removeTasker(taskerId);
          },
        },
      ],
    });
  };

  /**
   * Handles pull-to-refresh functionality
   * Purpose: Refreshes the favorite taskers list
   * @returns {Promise<void>} No return value, updates list data
   */
  const onRefresh = (): void => {
    setRefreshing(true);
    getFavoriteTasker();
    // Use timeout to ensure refreshing state is visible
    setTimeout(() => setRefreshing(false), 1000);
  };

  /**
   * Handles navigation to chat with tasker
   * Purpose: Opens chat screen with selected tasker
   * @param taskerId - ID of tasker to chat with
   * @returns {void} No return value, navigates to chat
   */
  const onNavigateChat = (taskerId: string): void => {
    // navigation?.navigate(RouteName.Chat, {
    //   memberIds: [taskerId || ''],
    //   isTaskerFavorite: true,
    //   entryPoint: TrackingScreenNames.FavoriteTasker,
    // });
  };

  /**
   * Opens introduction screen for favorite taskers
   * Purpose: Shows educational content about favorite taskers feature
   * @returns {void} No return value, navigates to intro screen
   */
  const openModalIntro = (): void => {
    NavigationService.navigate(RouteNameSystem.TabAccountNavigator, {
      screen: RouteName.IntroFavoriteTaskerScreen,
    });
  };

  /**
   * Renders individual favorite tasker item
   * Purpose: Creates list item component for FlatList
   * @param item - Tasker data object
   * @param index - Index in the list
   * @returns {JSX.Element} React component for list item
   */
  const renderItem = ({
    item,
    index,
  }: {
    item: IUser;
    index: number;
  }): JSX.Element => (
    <FavoriteTaskerItem
      tasker={item}
      index={index}
      onRemoveFavoriteTasker={() => handleRemoveFavoriteTasker(item?._id)}
      onNavigateChat={() => onNavigateChat(item?._id || '')}
    />
  );

  /**
   * Renders separator between list items
   * Purpose: Adds spacing between favorite tasker items
   * @returns {JSX.Element} React component for separator
   */
  const renderSeparator = (): JSX.Element => (
    <SizedBox height={Spacing.SPACE_08} />
  );

  /**
   * Renders footer component for the list
   * Purpose: Shows empty state when there are few favorite taskers
   * @returns {JSX.Element} React component for list footer
   */
  const renderFooter = (): JSX.Element => (
    <FavoriteTaskerEmpty
      isShow={favoriteTaskers?.length <= MAX_SHOW_DETAIL}
      onOpenIntro={openModalIntro}
    />
  );

  // Flow use bTaskee not login
  if (!user?._id) {
    return <NotLoggedIn />;
  }

  if (isGetApi) return <PlaceholderLoader />;

  return (
    <BlockView
      inset={'bottom'}
      style={styles.container}
    >
      <BlockView
        flex
        padding={{ bottom: Spacing.SPACE_16 }}
      >
        <FlatList
          contentContainerStyle={styles.contentContainerStyle}
          data={favoriteTaskers}
          renderItem={renderItem}
          keyExtractor={(item: IUser, index: number) => `${item._id}-${index}`}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
            />
          }
          ItemSeparatorComponent={renderSeparator}
          ListFooterComponent={renderFooter}
          scrollIndicatorInsets={{ right: 1 }}
        />
      </BlockView>
      <ButtonAddFavoriteTasker
        onPress={openModalAddFavorite}
        isDisabled={disabledAdd}
        user={mockUser}
        onNavigateHome={() => {
          // Navigate to home screen
        }}
      />
      <ModalAddTasker
        ref={modalAddFavorite}
        allTaskerList={allTaskers}
        onAddTasker={addTaskerToFavorite}
        title={t('ADD_FAVORITE_TASKER_TITLE')}
      />
    </BlockView>
  );
};
