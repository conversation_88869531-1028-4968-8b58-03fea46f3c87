import React, { useState } from 'react';
import {
  ActivityIndicator,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@btaskee/auth-store';
import {
  Alert,
  ApiClient,
  useI18n,
  useUserStore,
} from '@btaskee/design-system';

import { loginWithPhoneNumber } from '../apis/login-phone-number';
import { COUNTRY_CODE_VN, refactorPhoneNumber } from '../lib/helper';
import { icArrowLeft, icFlagVN } from '../lib/helper/assets/images';
import { SHA256 } from '../lib/helper/sha256';

const LoginScreen = ({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) => {
  const [phone, setPhone] = useState('**********');
  const [password, setPassword] = useState('123456');
  const [secureEntry, setSecureEntry] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuth();
  const { t } = useI18n('common');
  const { getFinancialAccount, getUser } = useUserStore();

  const handleLogin = async () => {
    if (!phone || !password) {
      Alert.alert.open({
        title: t('ALERT_TITLE'),
        message: t('ALERT_MESSAGE'),
      });
      return;
    }
    setIsLoading(true);
    try {
      const response = await loginWithPhoneNumber({
        username: refactorPhoneNumber(phone, COUNTRY_CODE_VN),
        password: SHA256(password),
        type: 'ASKER',
      });
      if (response?.token && response?.refreshToken) {
        ApiClient.updateToken(response.token);
        await login(response.token, response.refreshToken, response.userId);

        // Get user info
        await getUser();

        // Get financial account
        await getFinancialAccount();
        route.params?.onLoginSuccess(response);
        return;
      }
      throw new Error('Invalid response');
    } catch (error: any) {
      Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: error?.message || t('ERROR_TRY_AGAIN'),
        actions: [
          {
            text: t('CLOSE'),
            onPress: () => {},
          },
        ],
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View>
        <View style={styles.header}>
          <TouchableOpacity
            hitSlop={10}
            onPress={() => navigation?.goBack?.()}
          >
            <Image
              source={icArrowLeft}
              style={styles.iconBack}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Đăng nhập</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.content}>
          <Text style={styles.title}>Mừng trở lại,</Text>
          <Text style={styles.subtitle}>Vui lòng đăng nhập để tiếp tục</Text>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Số điện thoại</Text>
            <View style={styles.phoneContainer}>
              <View style={styles.countryPicker}>
                <Image
                  source={icFlagVN}
                  style={{ width: 20, height: 15 }}
                  resizeMode="contain"
                />
                <Text style={styles.countryCode}>+84</Text>
              </View>
              <TextInput
                style={styles.phoneInput}
                placeholder="Nhập số điện thoại"
                keyboardType="phone-pad"
                value={phone}
                onChangeText={setPhone}
              />
            </View>
          </View>
          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Mật khẩu</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={styles.passwordInput}
                placeholder="Nhập mật khẩu"
                secureTextEntry={secureEntry}
                value={password}
                onChangeText={setPassword}
              />
              <TouchableOpacity onPress={() => setSecureEntry((prev) => !prev)}>
                <></>
              </TouchableOpacity>
            </View>
          </View>
          <TouchableOpacity
            style={styles.loginButton}
            onPress={handleLogin}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator
                size="small"
                color="#1BB55C"
              />
            ) : (
              <Text style={styles.loginButtonText}>Đăng nhập</Text>
            )}
          </TouchableOpacity>
        </View>
        <View style={styles.footer}>
          <TouchableOpacity onPress={() => navigation.navigate('Register')}>
            <Text style={styles.footerText}>
              Bạn chưa có tài khoản?{' '}
              <Text style={styles.footerLink}>Tạo tài khoản</Text>
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.footerLink}>Quên mật khẩu</Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 18,
    marginBottom: 4,
    fontFamily: 'Montserrat-Bold',
  },
  input: {
    marginBottom: 16,
    backgroundColor: '#fff',
  },
  button: {
    marginVertical: 8,
    borderRadius: 8,
    backgroundColor: '#000',
  },
  link: {
    color: '#6200ee',
    textAlign: 'center',
    marginTop: 8,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  content: {
    paddingHorizontal: 16,
  },
  headerTitle: {
    fontSize: 18,
    color: '#000',
    fontFamily: 'Montserrat-Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#000',
    marginBottom: 24,
    fontFamily: 'Montserrat-Medium',
  },
  fieldContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
    fontFamily: 'Montserrat-Medium',
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryPicker: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 42,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: '#EBEBED',
  },
  flagEmoji: {
    fontSize: 16,
    marginRight: 8,
  },
  countryCode: {
    fontSize: 14,
    color: '#000',
    marginLeft: 4,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#000',
    height: 42,
    borderWidth: 1,
    borderColor: '#D3D3D3',
    borderRadius: 8,
    marginLeft: 8,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFF',
    height: 42,
    paddingHorizontal: 12,
  },
  passwordInput: {
    flex: 1,
    fontSize: 14,
    color: '#000',
  },
  loginButton: {
    height: 48,
    borderRadius: 8,
    backgroundColor: '#EBEBED',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 24,
    marginBottom: 16,
  },
  loginButtonText: {
    fontSize: 14,
    color: '#1BB55C',
    fontFamily: 'Montserrat-Bold',
  },
  footer: {
    alignItems: 'center',
    paddingBottom: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#000',
    fontFamily: 'Montserrat-Medium',
  },
  footerLink: {
    fontSize: 14,
    color: '#1BB55C',
    marginTop: 8,
    fontFamily: 'Montserrat-Bold',
  },
  iconBack: {
    width: 24,
    height: 24,
    tintColor: '#000',
  },
});

export default LoginScreen;
