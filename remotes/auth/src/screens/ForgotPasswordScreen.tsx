import React, { useState } from 'react';
import {
  Alert,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { forgotPassword } from '../apis/forgot-password';
import { COUNTRY_CODE_VN, getPhoneNumber } from '../lib/helper';
import { icArrowLeft, icFlagVN } from '../lib/helper/assets/images';

const ForgotPasswordScreen = ({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) => {
  const [phone, setPhone] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Regex cho số điện thoại Việt Nam
  const validateVietnamesePhone = (phoneNumber: string) => {
    // Regex cho số điện thoại Việt Nam (bắt đầu với 84 hoặc 0, theo sau là 9 chữ số)
    const vietnamPhoneRegex =
      /^(84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/;
    return vietnamPhoneRegex.test(phoneNumber);
  };

  const navigateToOTP = (response: any) => {
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'OTP',
          params: {
            phone,
            countryCode: COUNTRY_CODE_VN,
            supplier: response?.supplier,
            isForgotPassword: true,
          },
        },
      ],
    });
  };

  const handleSendOTP = async () => {
    if (!phone) {
      Alert.alert('Vui lòng nhập số điện thoại');
      return;
    }

    if (!validateVietnamesePhone(phone)) {
      Alert.alert('Số điện thoại không hợp lệ');
      return;
    }

    setIsLoading(true);
    try {
      const response = await forgotPassword({
        phone: getPhoneNumber(phone, COUNTRY_CODE_VN),
        countryCode: COUNTRY_CODE_VN,
        type: 'ASKER',
      });
      navigateToOTP(response);
    } catch (error: any) {
      Alert.alert('Rất tiếc', error?.message || 'Có lỗi xảy ra');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View>
        <View style={styles.header}>
          <TouchableOpacity
            hitSlop={10}
            onPress={() => navigation?.goBack?.()}
          >
            <Image
              source={icArrowLeft}
              style={styles.iconBack}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Quên mật khẩu</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.content}>
          <Text style={styles.title}>Khôi phục mật khẩu</Text>
          <Text style={styles.subtitle}>
            Nhập số điện thoại để nhận mã xác thực
          </Text>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Số điện thoại *</Text>
            <View style={styles.phoneContainer}>
              <View style={styles.countryPicker}>
                <Image
                  source={icFlagVN}
                  style={{ width: 20, height: 15 }}
                  resizeMode="contain"
                />
                <Text style={styles.countryCode}>+84</Text>
              </View>
              <TextInput
                style={styles.phoneInput}
                placeholder="Nhập số điện thoại"
                keyboardType="phone-pad"
                value={phone}
                onChangeText={setPhone}
              />
            </View>
          </View>

          <TouchableOpacity
            style={[styles.sendButton, !phone && styles.disabledButton]}
            onPress={handleSendOTP}
            disabled={isLoading || !phone}
          >
            {isLoading ? (
              <Text style={styles.sendButtonText}>Đang gửi...</Text>
            ) : (
              <Text style={styles.sendButtonText}>Gửi mã xác thực</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            onPress={() =>
              navigation.reset({ index: 0, routes: [{ name: 'Login' }] })
            }
          >
            <Text style={styles.footerText}>
              Nhớ mật khẩu? <Text style={styles.footerLink}>Đăng nhập</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  iconBack: {
    width: 24,
    height: 24,
  },
  headerTitle: {
    fontSize: 18,
    color: '#000',
    fontFamily: 'Montserrat-Bold',
  },
  content: {
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 18,
    marginBottom: 4,
    fontFamily: 'Montserrat-Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#000',
    marginBottom: 24,
    fontFamily: 'Montserrat-Medium',
  },
  fieldContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    color: '#000',
    marginBottom: 8,
    fontFamily: 'Montserrat-Medium',
  },
  phoneContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  countryPicker: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 16,
    backgroundColor: '#F5F5F5',
    borderRightWidth: 1,
    borderRightColor: '#E0E0E0',
  },
  countryCode: {
    marginLeft: 8,
    fontSize: 16,
    color: '#000',
    fontFamily: 'Montserrat-Medium',
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: 12,
    paddingVertical: 16,
    fontSize: 16,
    color: '#000',
    fontFamily: 'Montserrat-Medium',
  },
  sendButton: {
    backgroundColor: '#1BB55C',
    paddingVertical: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 24,
  },
  disabledButton: {
    backgroundColor: '#CCCCCC',
  },
  sendButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'Montserrat-Bold',
  },
  footer: {
    alignItems: 'center',
    marginTop: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#666',
    fontFamily: 'Montserrat-Medium',
  },
  footerLink: {
    color: '#1BB55C',
    fontFamily: 'Montserrat-Bold',
  },
});

export default ForgotPasswordScreen;
