import React, { useState } from 'react';
import {
  Alert,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { OtpInput } from 'react-native-otp-entry';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@btaskee/auth-store';

import { resendActivationCode } from '../apis/resend-otp';
import { setUserPassword } from '../apis/set-user-password';
import { verifyOTP } from '../apis/verify-otp';
import { icArrowLeft } from '../lib/helper/assets/images';
import { SHA256 } from '../lib/helper/sha256';

const OTPScreen = ({ route, navigation }: { route: any; navigation: any }) => {
  const [otp, setOtp] = useState('');
  const [showPasswordScreen, setShowPasswordScreen] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [userId, setUserId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const { phone, supplier, isFromRegister, isForgotPassword, isSocialLogin } =
    route.params || {};
  const { login } = useAuth();

  const handleVerify = async () => {
    if (otp.length !== 6 || !/^\d{6}$/.test(otp)) {
      Alert.alert('Vui lòng nhập mã OTP 6 số');
      return;
    }

    setIsVerifying(true);
    try {
      const response = await verifyOTP({
        phone,
        countryCode: '+84',
        code: otp,
      });
      setUserId(response.userId);
      // Từ trang đăng ký + quên mật khẩu -> set password, còn lại về Home
      if (isFromRegister || isForgotPassword) {
        setShowPasswordScreen(true);
        return;
      }

      if (isSocialLogin) {
        // Đăng ký với social, set credentials và về Home
        route?.params?.onLoginSuccess?.();
        return;
      }
    } catch (error) {
      console.error('Lỗi OTP:', error);
      Alert.alert('Xác thực OTP thất bại');
    } finally {
      setIsVerifying(false);
    }
  };

  const handleSetPassword = async () => {
    if (!password || !confirmPassword) {
      Alert.alert('Vui lòng nhập đầy đủ thông tin');
      return;
    }
    if (password !== confirmPassword) {
      Alert.alert('Mật khẩu không khớp');
      return;
    }
    if (password.length < 6) {
      Alert.alert('Mật khẩu phải có ít nhất 6 ký tự');
      return;
    }

    setIsLoading(true);
    try {
      const response = await setUserPassword({
        userId,
        password: SHA256(password),
      });
      if (response?.token && response?.refreshToken) {
        // Set credentials và về Home
        await login(response.token, response.refreshToken, response?.userId);
        route?.params?.onLoginSuccess?.();
        return;
      }
      throw new Error('Invalid response');
    } catch (error) {
      console.error('Lỗi đặt mật khẩu:', error);
      Alert.alert('Đặt mật khẩu thất bại');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResend = async () => {
    try {
      await resendActivationCode({ phone, countryCode: '+84', supplier });
      Alert.alert(`Đã gửi lại OTP tới ${phone}`);
    } catch (error) {
      console.error('Lỗi gửi lại OTP:', error);
      Alert.alert('Gửi lại OTP thất bại');
    }
  };

  if (showPasswordScreen) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            hitSlop={10}
            onPress={() => route.params?.onExist?.()}
          >
            <Image
              source={icArrowLeft}
              style={styles.iconBack}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Đặt Mật Khẩu</Text>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.content}>
          {/* <Text style={styles.title}>Đặt Mật Khẩu</Text> */}
          <Text style={styles.subtitle}>
            Tạo mật khẩu để đăng nhập cho lần sau
          </Text>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nhập mật khẩu</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập mật khẩu"
              secureTextEntry
              value={password}
              onChangeText={setPassword}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.label}>Nhập lại mật khẩu</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập lại mật khẩu"
              secureTextEntry
              value={confirmPassword}
              onChangeText={setConfirmPassword}
            />
          </View>

          <TouchableOpacity
            onPress={handleSetPassword}
            style={[styles.button, isLoading && styles.disabledButton]}
            disabled={isLoading}
          >
            <Text
              style={[
                styles.buttonText,
                isLoading && styles.disabledButtonText,
              ]}
            >
              {isLoading ? 'Đang xử lý...' : 'Hoàn Thành'}
            </Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          hitSlop={10}
          onPress={() => route.params?.onExist?.()}
        >
          <Image
            source={icArrowLeft}
            style={styles.iconBack}
            resizeMode="contain"
          />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Nhập Mã OTP</Text>
        <View style={{ width: 24 }} />
      </View>
      <View style={styles.content}>
        {/* <Text style={styles.title}>Nhập Mã OTP</Text> */}
        <Text style={styles.subtitle}>Mã 6 số đã được gửi tới {phone}</Text>
        <OtpInput
          numberOfInputs={6}
          onTextChange={(text) => setOtp(text)}
          theme={{
            containerStyle: styles.otpContainer,
            pinCodeContainerStyle: styles.otpBox,
            pinCodeTextStyle: styles.otpText,
          }}
        />
        <TouchableOpacity
          onPress={handleVerify}
          style={[
            styles.button,
            (otp.length !== 6 || isVerifying) && styles.disabledButton,
          ]}
          disabled={otp.length !== 6 || isVerifying}
        >
          <Text
            style={[
              styles.buttonText,
              (otp.length !== 6 || isVerifying) && styles.disabledButtonText,
            ]}
          >
            {isVerifying ? 'Đang xác thực...' : 'Xác Nhận'}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity onPress={handleResend}>
          <Text style={styles.link}>Gửi Lại OTP</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  iconBack: {
    width: 24,
    height: 24,
  },
  headerTitle: {
    fontSize: 18,
    color: '#000',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  otpBox: {
    width: 40,
    height: 48,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    backgroundColor: '#f5f5f5',
  },
  otpText: {
    fontSize: 20,
    color: '#000',
    textAlign: 'center',
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#f5f5f5',
  },
  button: {
    marginVertical: 8,
    backgroundColor: '#6200ee',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  disabledButtonText: {
    color: '#999',
  },
  link: {
    color: '#6200ee',
    textAlign: 'center',
    marginTop: 8,
  },
});

export default OTPScreen;
