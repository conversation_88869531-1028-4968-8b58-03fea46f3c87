import React, { useState } from 'react';
import {
  Alert,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { checkReferralCode, register, RegisterOptions } from '../apis/register';
import {
  COUNTRY_CODE_VN,
  getPhoneNumber,
  refactorPhoneNumber,
} from '../lib/helper';
import { icArrowLeft, icFlagVN } from '../lib/helper/assets/images';

const RegisterScreen = ({
  navigation,
  route,
}: {
  navigation: any;
  route: any;
}) => {
  const [fullName, setFullName] = useState('Anh');
  const [phone, setPhone] = useState('0987860977');
  const [email, setEmail] = useState('');
  const [referralCode, setReferralCode] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const validateVietnamesePhone = (phoneNumber: string) => {
    // Regex cho số điện thoại Việt Nam (bắt đầu với 84 hoặc 0, theo sau là 9 chữ số)
    const vietnamPhoneRegex =
      /^(84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$/;
    return vietnamPhoneRegex.test(phoneNumber);
  };

  const validateForm = () => {
    if (!fullName || !phone) {
      Alert.alert('Vui lòng điền đầy đủ thông tin bắt buộc');
      return false;
    }
    if (!validateVietnamesePhone(phone)) {
      Alert.alert(
        'Số điện thoại không hợp lệ',
        'Vui lòng nhập số điện thoại Việt Nam hợp lệ',
      );
      return false;
    }
    if (!agreeTerms) {
      Alert.alert(
        'Vui lòng đồng ý với các Điều khoản & chính sách của bTaskee',
      );
      return false;
    }
    return true;
  };

  const buildRequestData = (): RegisterOptions => {
    const requestData: RegisterOptions = {
      countryCode: COUNTRY_CODE_VN,
      phone: getPhoneNumber(phone, COUNTRY_CODE_VN),
      username: refactorPhoneNumber(phone, COUNTRY_CODE_VN),
      name: fullName,
      language: 'vi',
      isoCode: 'VN',
      type: 'ASKER',
      loginType: 'password',
    };

    if (email.trim()) {
      requestData.email = email.trim();
    }

    if (referralCode.trim()) {
      requestData.signUpPromotionCode = referralCode.trim();
    }

    return requestData;
  };

  const navigateToOTP = (response: any) => {
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'OTP',
          params: {
            phone,
            countryCode: COUNTRY_CODE_VN,
            supplier: response.supplier,
            isFromRegister: true,
          },
        },
      ],
    });
  };

  const handleRegisterError = (error: any) => {
    const errorMessage = error?.message || 'Có lỗi xảy ra';
    Alert.alert('Rất tiếc', errorMessage);
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const requestData = buildRequestData();
      const response = await register(requestData);

      if (response.userId) {
        navigateToOTP(response);
        return;
      }

      throw new Error('Invalid response');
    } catch (error: any) {
      handleRegisterError(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View>
        <View style={styles.header}>
          <TouchableOpacity
            hitSlop={10}
            onPress={() => navigation?.goBack?.()}
          >
            <Image
              source={icArrowLeft}
              style={styles.iconBack}
              resizeMode="contain"
            />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Đăng ký</Text>
          <View style={{ width: 24 }} />
        </View>

        <View style={styles.content}>
          <Text style={styles.title}>Tạo tài khoản mới</Text>
          <Text style={styles.subtitle}>
            Vui lòng điền thông tin để đăng ký
          </Text>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Họ và tên *</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập họ và tên"
              value={fullName}
              onChangeText={setFullName}
            />
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Số điện thoại *</Text>
            <View style={styles.phoneContainer}>
              <View style={styles.countryPicker}>
                <Image
                  source={icFlagVN}
                  style={{ width: 20, height: 15 }}
                  resizeMode="contain"
                />
                <Text style={styles.countryCode}>+84</Text>
              </View>
              <TextInput
                style={styles.phoneInput}
                placeholder="Nhập số điện thoại"
                keyboardType="phone-pad"
                value={phone}
                onChangeText={setPhone}
              />
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập email (không bắt buộc)"
              keyboardType="email-address"
              autoCapitalize="none"
              value={email}
              onChangeText={setEmail}
            />
          </View>

          <View style={styles.fieldContainer}>
            <Text style={styles.label}>Mã giới thiệu</Text>
            <TextInput
              style={styles.input}
              placeholder="Nhập mã giới thiệu (không bắt buộc)"
              value={referralCode}
              onChangeText={setReferralCode}
            />
          </View>

          <View style={styles.checkboxContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => setAgreeTerms(!agreeTerms)}
            >
              <View
                style={[
                  styles.checkboxBox,
                  agreeTerms && styles.checkboxChecked,
                ]}
              >
                {agreeTerms && <Text style={styles.checkboxTick}>✓</Text>}
              </View>
            </TouchableOpacity>
            <Text style={styles.checkboxText}>
              Tôi đồng ý với các{' '}
              <Text style={styles.linkText}>Điều khoản & chính sách</Text> của
              bTaskee
            </Text>
          </View>

          <TouchableOpacity
            style={[
              styles.registerButton,
              (!fullName || !phone || !agreeTerms) && styles.disabledButton,
            ]}
            onPress={handleRegister}
            disabled={isLoading || !fullName || !phone || !agreeTerms}
          >
            {isLoading ? (
              <Text style={styles.registerButtonText}>Đang xử lý...</Text>
            ) : (
              <Text style={styles.registerButtonText}>Đăng ký</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.footer}>
          <TouchableOpacity
            onPress={() =>
              navigation.reset({ index: 0, routes: [{ name: 'Login' }] })
            }
          >
            <Text style={styles.footerText}>
              Đã có tài khoản? <Text style={styles.footerLink}>Đăng nhập</Text>
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  iconBack: {
    width: 24,
    height: 24,
  },
  headerTitle: {
    fontSize: 18,
    color: '#000',
    fontFamily: 'Montserrat-Bold',
  },
  content: {
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 18,
    marginBottom: 4,
    fontFamily: 'Montserrat-Bold',
  },
  subtitle: {
    fontSize: 14,
    color: '#000',
    marginBottom: 24,
    fontFamily: 'Montserrat-Medium',
  },
  fieldContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 8,
    fontFamily: 'Montserrat-Medium',
  },
  input: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  phoneContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryPicker: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginRight: 8,
    backgroundColor: '#F5F5F5',
  },
  countryCode: {
    marginLeft: 8,
    fontSize: 16,
    color: '#000',
  },
  phoneInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  checkbox: {
    marginRight: 12,
    marginTop: 2,
  },
  checkboxBox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#E0E0E0',
    borderRadius: 4,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#1BB55C',
    borderColor: '#1BB55C',
  },
  checkboxTick: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  checkboxText: {
    flex: 1,
    fontSize: 14,
    color: '#000',
    lineHeight: 20,
    fontFamily: 'Montserrat-Medium',
  },
  linkText: {
    color: '#1BB55C',
    textDecorationLine: 'underline',
  },
  registerButton: {
    backgroundColor: '#1BB55C',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24,
  },
  disabledButton: {
    backgroundColor: '#E0E0E0',
  },
  registerButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    fontFamily: 'Montserrat-Bold',
  },
  footer: {
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 14,
    color: '#000',
    fontFamily: 'Montserrat-Medium',
  },
  footerLink: {
    color: '#1BB55C',
    fontWeight: 'bold',
  },
});

export default RegisterScreen;
