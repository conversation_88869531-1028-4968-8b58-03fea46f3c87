export const COUNTRY_CODE_VN = '+84';
export const COUNTRY_CODE_TH = '+66';
export const COUNTRY_CODE_ID = '+62';
export const COUNTRY_CODE_MY = '+60';

/**
 * @description refactor PhoneNumber with countryCode
 * @param phoneNumber - The phone number to format
 * @param countryCode - The country code (e.g., '+84', '+66')
 * @returns Formatted phone number with 0 prefix for VN, TH, ID, MY
 * @example
 * getPhoneNumber('987860977', '+84') => '0987860977'
 * getPhoneNumber('0987860977', '+84') => '0987860977'
 */
export const getPhoneNumber = (
  phoneNumber: string | number,
  countryCode: string,
): string => {
  const phoneStr = String(phoneNumber);
  const countryCodeStr = String(countryCode);

  // List of country codes that require 0 prefix
  const countriesRequiringPrefix = [
    COUNTRY_CODE_VN,
    COUNTRY_CODE_TH,
    COUNTRY_CODE_ID,
    COUNTRY_CODE_MY,
  ];

  // Auto add 0 prefix for specific countries if not already present
  if (
    countriesRequiringPrefix.includes(countryCodeStr) &&
    !phoneStr.startsWith('0')
  ) {
    return '0' + phoneStr;
  }

  return phoneStr;
};

/**
 * @description refactor PhoneNumber with countryCode to international format
 * @param phoneNumber - The phone number to format
 * @param countryCode - The country code (e.g., '+84', '+66')
 * @returns International format phone number or original if no country code
 * @example
 * refactorPhoneNumber('0987860977', '+84') => '84987860977'
 * refactorPhoneNumber('987860977', '+84') => '84987860977'
 */
export const refactorPhoneNumber = (
  phoneNumber: string | number,
  countryCode?: string,
): string => {
  const phoneStr = String(phoneNumber);

  if (!countryCode) {
    return phoneStr;
  }

  const countryCodeStr = String(countryCode);
  const numericCountryCode = countryCodeStr.match(/\d+/)?.[0] || '';

  // List of country codes that may have 0 prefix to remove
  const countriesWithPrefix = [
    COUNTRY_CODE_VN,
    COUNTRY_CODE_TH,
    COUNTRY_CODE_ID,
    COUNTRY_CODE_MY,
  ];

  let formattedPhone = phoneStr;

  // Remove leading 0 for specific countries
  if (
    countriesWithPrefix.includes(countryCodeStr) &&
    phoneStr.startsWith('0')
  ) {
    formattedPhone = phoneStr.slice(1);
  }

  return `${numericCountryCode}${formattedPhone}`;
};
