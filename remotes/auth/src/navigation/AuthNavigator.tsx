import React from 'react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import ForgotPasswordScreen from '../screens/ForgotPasswordScreen';
import LoginScreen from '../screens/LoginScreen';
import OTPScreen from '../screens/OTPScreen';
import RegisterScreen from '../screens/RegisterScreen';

export type AuthStackParamList = {
  Login: undefined;
  OTP: { email: string };
  Register: undefined;
  ForgotPassword: undefined;
};

const Auth = createNativeStackNavigator<AuthStackParamList>();

const AuthNavigator = (props: any) => {
  return (
    <Auth.Navigator screenOptions={{ headerShown: false }}>
      <Auth.Screen
        initialParams={props}
        name="Login"
        component={LoginScreen}
        options={{ headerShown: false }}
      />
      <Auth.Screen
        initialParams={props}
        name="OTP"
        component={OTPScreen}
      />
      <Auth.Screen
        initialParams={props}
        name="Register"
        component={RegisterScreen}
      />
      <Auth.Screen
        initialParams={props}
        name="ForgotPassword"
        component={ForgotPasswordScreen}
      />
    </Auth.Navigator>
  );
};

export default AuthNavigator;
