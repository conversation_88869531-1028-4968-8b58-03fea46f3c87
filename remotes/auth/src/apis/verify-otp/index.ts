import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export type RegisterOptions = {
  countryCode: string;
  phone: string;
  code: string;
  appVersion?: string;
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const verifyOTP = async (dataOTP: RegisterOptions) => {
  dataOTP.appVersion = '3.40.0';
  const combine: Record<
    CountryCode,
    (dataOTP: RegisterOptions) => Promise<any>
  > = {
    VN: VN_API.verifyOTP,
    TH: TH_API.verifyOTP,
    ID: ID_API.verifyOTP,
    MY: MY_API.verifyOTP,
  };
  return await combine.VN(dataOTP);
};
