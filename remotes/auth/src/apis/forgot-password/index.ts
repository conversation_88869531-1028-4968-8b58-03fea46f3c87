import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export type ForgotPasswordOptions = {
  countryCode: string;
  phone: string;
  type: 'ASKER';
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const forgotPassword = async (
  dataForgotPassword: ForgotPasswordOptions,
) => {
  const combine: Record<
    CountryCode,
    (dataForgotPassword: ForgotPasswordOptions) => Promise<any>
  > = {
    VN: VN_API.forgotPassword,
    TH: TH_API.forgotPassword,
    ID: ID_API.forgotPassword,
    MY: MY_API.forgotPassword,
  };
  return await combine.VN(dataForgotPassword);
};
