import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export type SetUserPasswordOptions = {
  userId: string;
  password: string;
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const setUserPassword = async (
  dataSetPassword: SetUserPasswordOptions,
) => {
  const combine: Record<
    CountryCode,
    (dataSetPassword: SetUserPasswordOptions) => Promise<any>
  > = {
    VN: VN_API.setUserPassword,
    TH: TH_API.setUserPassword,
    ID: ID_API.setUserPassword,
    MY: MY_API.setUserPassword,
  };
  return await combine.VN(dataSetPassword);
};
