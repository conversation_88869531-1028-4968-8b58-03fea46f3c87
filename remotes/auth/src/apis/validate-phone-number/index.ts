import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export type RegisterOptions = {
  countryCode: string;
  phone: string;
  username: string;
  name: string;
  language: string;
  isoCode: string;
  type: string;
  loginType: string;
  referralCode?: string;
  email?: string;
  signUpPromotionCode?: string;
  appVersion?: string;
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const register = async (dataRegister: RegisterOptions) => {
  const combine: Record<
    CountryCode,
    (dataRegister: RegisterOptions) => Promise<any>
  > = {
    VN: VN_API.register,
    TH: TH_API.register,
    ID: ID_API.register,
    MY: MY_API.register,
  };
  return await combine.VN(dataRegister);
};

/**
 * @param referralCode
 */
export const checkReferralCode = async (referralCode: string) => {
  const combine: Record<CountryCode, (referralCode: string) => Promise<any>> = {
    VN: VN_API.checkReferralCode,
    TH: TH_API.checkReferralCode,
    ID: ID_API.checkReferralCode,
    MY: MY_API.checkReferralCode,
  };
  return await combine.VN(referralCode);
};
