import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

type LoginOptions = {
  username: string;
  password: string;
  type: string;
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const login = async (options: LoginOptions) => {
  const combine: Record<CountryCode, (options: LoginOptions) => Promise<any>> =
    {
      VN: VN_API.login,
      TH: TH_API.login,
      ID: ID_API.login,
      MY: MY_API.login,
    };
  return await combine.VN(options);
};
