import * as ID_API from './id';
import * as MY_API from './my';
import * as TH_API from './th';
import * as VN_API from './vn';

export type RegisterOptions = {
  phone: string;
  countryCode: string;
  appVersion?: string;
  supplier?: string;
};

type CountryCode = 'VN' | 'TH' | 'ID' | 'MY';

export const resendActivationCode = async (dataRegister: RegisterOptions) => {
  dataRegister.appVersion = '3.40.0';
  const combine: Record<
    CountryCode,
    (dataRegister: RegisterOptions) => Promise<any>
  > = {
    VN: VN_API.resendActivationCode,
    TH: TH_API.resendActivationCode,
    ID: ID_API.resendActivationCode,
    MY: MY_API.resendActivationCode,
  };
  return await combine.VN(dataRegister);
};
