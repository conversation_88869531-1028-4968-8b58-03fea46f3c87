import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

const ACCESS_KEY =
  '1JtpyvG4nr7gJfTkWl57JrdwN5RGuunhHGnWEpTPVofIzyqgXmPCus3IdgfpZGan';
const API_URL = 'http://192.168.88.71';

type FetchOptions = {
  method?: string;
  headers?: Record<string, string>;
  data?: any;
};

const log = (...args: any) => {
  if (__DEV__) {
    console.log(...args);
  }
};

export const fetchAPI = async <T>(
  url: string,
  options: FetchOptions = {},
): Promise<T> => {
  const { method = 'POST', headers = {}, data } = options;
  const fullUrl = `${API_URL}/api/${url}`;

  console.log('LOG -> fullUrl:', fullUrl);
  const config: AxiosRequestConfig = {
    url: fullUrl,
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers,
      accessKey: ACCESS_KEY,
    },
    data,
  };

  // Log request
  log('📤 Request:', {
    url,
    method,
    headers: config.headers,
    data: data ? JSON.stringify(data, null, 2) : undefined,
  });

  try {
    const response: AxiosResponse<T> = await axios(config);

    // Log response
    log('📥 Response:', {
      url,
      status: response.status,
      headers: response.headers,
      data: response?.data || null,
    });

    return response.data;
  } catch (error: any) {
    log('📥 Error:', {
      url,
      status: error?.response?.status,
      headers: error?.response?.headers,
      data: error?.response?.data || null,
    });
    throw error?.response?.data?.error;
  }
};
