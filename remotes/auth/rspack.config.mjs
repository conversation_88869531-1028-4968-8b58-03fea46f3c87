/* eslint-disable no-undefined */
import { getSharedDependencies } from '@btaskee/sdk';
import * as Repack from '@callstack/repack';
import { ReanimatedPlugin } from '@callstack/repack-plugin-reanimated';
import rspack from '@rspack/core';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const STANDALONE = Boolean(process.env.STANDALONE);

/**
 * Rspack configuration enhanced with Re.Pack defaults for React Native.
 *
 * Learn about Rspack configuration: https://rspack.dev/config/
 * Learn about Re.Pack configuration: https://re-pack.dev/docs/guides/configuration
 */

export default (env) => {
  const { mode } = env;
  const isDev = mode === 'development';

  return {
    mode,
    context: __dirname,
    entry: './index.js',
    experiments: {
      incremental: mode === 'development',
    },
    resolve: {
      ...Repack.getResolveOptions(),
      alias: {
        '@navigation': path.resolve(__dirname, './src/navigation'),
        '@app': path.resolve(__dirname, './src/App.tsx'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@screens': path.resolve(__dirname, './src/screens'),
        '@images': path.resolve(__dirname, './src/assets/images'),
        '@components': path.resolve(__dirname, './src/components'),
        '@i18n': path.resolve(__dirname, './src/i18n'),
      },
    },
    output: {
      uniqueName: 'sas-auth',
      path: path.resolve(__dirname, 'dist'),
    },
    module: {
      rules: [
        ...Repack.getJsTransformRules(),
        ...Repack.getAssetTransformRules({ inline: !STANDALONE }),
      ],
    },
    plugins: [
      new Repack.RepackPlugin(),
      new ReanimatedPlugin(),
      new Repack.plugins.ModuleFederationPluginV2({
        name: 'auth',
        filename: 'auth.container.js.bundle',
        dts: false,
        exposes: STANDALONE
          ? undefined
          : {
              './App': './src/navigation/AuthNavigator.tsx',
            },
        shared: getSharedDependencies({ eager: isDev }),
      }),
      // silence missing @react-native-masked-view optionally required by @react-navigation/elements
      new rspack.IgnorePlugin({
        resourceRegExp: /^@react-native-masked-view/,
      }),
    ],
  };
};
