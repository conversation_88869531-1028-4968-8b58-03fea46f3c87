{"name": "auth", "targets": {"start": {"executor": "nx:run-commands", "options": {"command": "yarn start", "cwd": "remotes/auth"}}, "install": {"executor": "nx:run-commands", "options": {"command": "yarn install", "cwd": "remotes/auth"}}, "build": {"executor": "nx:run-commands", "options": {"command": "yarn build", "cwd": "remotes/auth"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "yarn deploy", "cwd": "remotes/auth"}}, "reset": {"executor": "nx:run-commands", "options": {"command": "yarn reset", "cwd": "remotes/auth"}}}, "tags": ["scope:auth"]}