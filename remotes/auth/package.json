{"name": "auth", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "lint:fix": "eslint . --fix", "start": "react-native start --port 9001 --reset-cache", "test": "jest", "bundle:ios": "react-native bundle --platform ios --entry-file index.js --dev false", "bundle:android": "react-native bundle --platform android --entry-file index.js --dev false", "build": "yarn bundle:ios && yarn bundle:android && mkdir -p ../../deploy/firebase/auth && cp -r dist/* ../../deploy/firebase/auth/", "align-deps": "rnx-align-deps --write", "check-deps": "rnx-align-deps", "build:remote:ios": "PLATFORM=ios yarn rspack build --config rspack.config.js", "build:remote:android": "PLATFORM=android yarn rspack build --config rspack.config.js", "build:remote": "yarn build:remote:ios && yarn build:remote:android", "serve": "npx serve -p 9001", "reset": "rm -rf $TMPDIR/react-* && watchman watch-del-all && rm -rf node_modules && rm -rf ios/build && rm -rf ios/Pods && pnpm install && pnpm pod-install && cd android && rm -rf app/build && cd ../ && npx patch-package && echo '✅ Reset done'", "pod-install": "cd ios &&  pod install"}, "dependencies": {"@bottom-tabs/react-navigation": "0.9.1", "@btaskee/auth-store": "0.0.1", "@btaskee/design-system": "0.0.11", "@module-federation/enhanced": "0.11.3", "@react-native-community/datetimepicker": "8.3.0", "@react-navigation/native": "7.0.14", "@react-navigation/native-stack": "7.2.0", "@rnx-kit/align-deps": "2.5.1", "axios": "1.8.4", "buffer": "6.0.3", "dayjs": "1.9.6", "i18next": "25.1.3", "lodash": "4.17.16", "lottie-react-native": "7.2.2", "react": "18.3.1", "react-i18next": "15.5.1", "react-native": "0.77.2", "react-native-audio-recorder-player": "3.6.12", "react-native-calendars": "1.1312.0", "react-native-config": "1.5.5", "react-native-curved-bottom-bar": "3.5.1", "react-native-device-info": "14.0.4", "react-native-fast-image": "8.6.3", "react-native-fs": "2.20.0", "react-native-keychain": "10.0.0", "react-native-markdown-display": "7.0.2", "react-native-mmkv": "3.2.0", "react-native-modal": "14.0.0-rc.1", "react-native-otp-entry": "1.8.4", "react-native-permissions": "5.4.0", "react-native-reanimated": "3.17.5", "react-native-reanimated-carousel": "4.0.2", "react-native-safe-area-context": "5.4.0", "react-native-screens": "4.10.0", "react-native-sound": "0.11.2", "react-native-tab-view": "4.1.0", "react-native-vector-icons": "10.2.0", "vietnamese-lunar-calendar": "0.0.6", "zustand": "5.0.3"}, "devDependencies": {"@babel/core": "7.25.2", "@babel/preset-env": "7.25.3", "@babel/runtime": "7.25.0", "@btaskee/config": "0.1.28", "@btaskee/sdk": "0.0.7", "@callstack/repack": "5.1.0", "@callstack/repack-plugin-reanimated": "5.1.1", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.2", "@react-native/codegen": "0.79.2", "@react-native/eslint-config": "0.77.2", "@react-native/eslint-plugin": "0.77.2", "@react-native/gradle-plugin": "0.79.2", "@react-native/metro-config": "0.77.2", "@react-native/typescript-config": "0.77.2", "@rspack/core": "1.2.8", "@swc/helpers": "0.5.15", "@types/jest": "29.5.13", "@types/react": "18.2.6", "@types/react-test-renderer": "18.0.0", "@typescript-eslint/eslint-plugin": "7.1.1", "@typescript-eslint/parser": "7.1.1", "babel-jest": "29.6.3", "babel-plugin-transform-inline-environment-variables": "0.4.4", "eslint": "8.43.0", "eslint-config-prettier": "8.5.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-extra-rules": "0.0.0-development", "eslint-plugin-ft-flow": "2.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "27.9.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-react": "7.30.1", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-native": "4.0.0", "eslint-plugin-simple-import-sort": "12.1.1", "husky": "8.0.3", "jest": "29.6.3", "patch-package": "8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "rnx-kit": {"kitType": "app", "alignDeps": {"presets": ["./node_modules/@btaskee/sdk/preset"], "requirements": ["@btaskee/sdk@0.0.7"], "capabilities": ["super-app"]}}}