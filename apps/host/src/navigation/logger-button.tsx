/**
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-09-24 13:57:53
 * @modify date 2024-09-24 13:57:53
 * @desc Button show network info, only show when env = sandbox
 */
import React from 'react';
import FAB from 'react-native-animated-fab';
import {
  BlockView,
  DeviceHelper,
  NavigationService,
} from '@btaskee/design-system';

import { network } from '@images';

export const LoggerButton = (WrappedComponent: () => React.JSX.Element) => {
  return (
    <>
      <WrappedComponent />
      <BlockView>
        <FAB
          bottomOffset={Math.round(DeviceHelper.WINDOW.HEIGHT / 4.5)}
          renderSize={60}
          borderRadius={30}
          onPress={() => NavigationService.navigate('NetworkLogger')}
          icon={network}
          iconSize={40}
          tintColor={'#ffffff'}
        />
      </BlockView>
    </>
  );
};
