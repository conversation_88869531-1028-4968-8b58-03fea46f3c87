import React from 'react';
import { useRoute } from '@react-navigation/native';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const Account = React.lazy(() => import('account/AccountTab'));

export function TabAccountScreen(): React.JSX.Element {
  const route = useRoute();
  return (
    <ErrorBoundary name="AccountScreen">
      <React.Suspense fallback={<LoadingMiniApp />}>
        <Account {...route.params} />
      </React.Suspense>
    </ErrorBoundary>
  );
}
