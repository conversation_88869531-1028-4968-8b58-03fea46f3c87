import React from 'react';
import { RouteName } from '@btaskee/design-system';
import { useRoute } from '@react-navigation/native';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const TabAccountNavigator = React.lazy(() => import('account/MainNavigator'));

export function TabAccountNavigatorScreen(): React.JSX.Element {
  const route = useRoute();
  return (
    <ErrorBoundary name={RouteName.TabAccount}>
      <React.Suspense fallback={<LoadingMiniApp />}>
        <TabAccountNavigator {...route.params} />
      </React.Suspense>
    </ErrorBoundary>
  );
}
