import React from 'react';
import { RouteName } from '@btaskee/design-system';
import { useRoute } from '@react-navigation/native';

import { ErrorBoundary, LoadingMiniApp } from '@components';

const BReward = React.lazy(() => import('bReward/MainNavigator'));

export function HomeScreen() {
  const route = useRoute();

  return (
    <ErrorBoundary name={RouteName.BReward}>
      <React.Suspense fallback={<LoadingMiniApp />}>
        <BReward {...route.params} />
      </React.Suspense>
    </ErrorBoundary>
  );
}
