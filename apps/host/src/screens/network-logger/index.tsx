import React from 'react';
import NetworkLogger from 'react-native-network-logger';
import { BlockView, NavBar } from '@btaskee/design-system';

export const LoggerScreen = ({ navigation }: any) => {
  return (
    <BlockView flex>
      <NavBar
        onGoBack={() => navigation.goBack()}
        title={'Network Logger'}
      />
      <NetworkLogger
        compact
        theme="light"
        maxRows={20}
      />
    </BlockView>
  );
};
