
import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import * as Keychain from "react-native-keychain";
import { AuthState } from "./types";

export const useAuth = create<AuthState>()(
    persist(
      (set) => ({
        isAuthenticated: false,
        token: null,
        refreshToken: null,
        userId: null,
        login: async (token: string, refreshToken: string, userId: string) => {
          set({ token, refreshToken, userId, isAuthenticated: true });
        },

        logout: async () => {
          await Keychain.resetGenericPassword();
          set({ token: null, refreshToken: null, userId: null, isAuthenticated: false });
        },

        rehydrate: async () => {
          try {
          } catch (e) {
          }
        },
      }),
      {
        name: "auth-store",
        storage: createJSONStorage(() => ({
          getItem: async (key) => {
            const credentials = await Keychain.getGenericPassword({
              service: key,
            });
            if (credentials) {
              const parsedData = credentials.password;
              return JSON.parse(parsedData ?? '{}');
            }
            return null;
          },
          setItem: async (key, value) => {
            await Keychain.setGenericPassword('name-auth-store', JSON.stringify(value), {
                service: key,
              });
          },
          removeItem: async (key) => {
             await Keychain.resetGenericPassword();
          },
          version: 1,
        })),
      }
    ));
