import React, { useEffect } from 'react';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

import { styles } from './styles';

export interface SkeletonBoxProps {
  width?: number | string;
  height?: number;
  style?: any;
}

export const SkeletonBox = ({
  width,
  height = 16,
  style,
  ...props
}: SkeletonBoxProps) => {
  const opacity = useSharedValue(0.3);

  useEffect(() => {
    opacity.value = withRepeat(withTiming(0.8, { duration: 1000 }), -1, true);
  }, [opacity]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: opacity.value,
    };
  });

  return (
    <Animated.View
      style={[styles.skeletonBox, { width, height }, animatedStyle, style]}
      {...props}
    />
  );
};
