import React, { memo } from 'react';
import LinearGradient from 'react-native-linear-gradient';
import Animated, {
  Extrapolation,
  interpolate,
  useAnimatedStyle,
} from 'react-native-reanimated';

import { IconAssets } from '../../assets';
import { ThemeHelpers } from '../../helpers';
import { BorderRadius, ColorsV2 } from '../../tokens';
import { BlockView } from '../block-view';
import { FastImage } from '../fast-image';
import { IconImage } from '../icon-image';
import { TouchableOpacity } from '../touchable-opacity';
import useHeaderAnimated from './hook';
import styles, { height } from './styles';

interface HeaderAnimatedProps {
  backgroundImage: string;
  onPressBack: () => void;
  children?: React.ReactNode;
  footer?: React.ReactNode;
}

export const HeaderAnimated = memo(
  ({ backgroundImage, onPressBack, children, footer }: HeaderAnimatedProps) => {
    const { onScroll, scrollY } = useHeaderAnimated();

    const animatedLinearStyle = useAnimatedStyle(() => {
      const yValue = interpolate(
        scrollY.value,
        [0, 200],
        [0, 200],
        Extrapolation.CLAMP,
      );
      return {
        height: height - yValue,
        overflow: 'hidden',
      };
    });

    const animatedContainerStyle = useAnimatedStyle(() => {
      const yValue = interpolate(
        scrollY.value,
        [0, 200],
        [0, 200],
        Extrapolation.CLAMP,
      );
      return {
        paddingTop: yValue,
      };
    });

    return (
      <BlockView flex={1}>
        <Animated.View style={animatedLinearStyle}>
          <LinearGradient
            colors={[
              ThemeHelpers.hexWithOpacity(ColorsV2.neutralWhite, 0),
              ColorsV2.neutralWhite,
            ]}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
            locations={[0.36, 0.94]}
            style={styles.linearGradient}
          />
          <FastImage
            source={{ uri: backgroundImage }}
            style={styles.image}
            resizeMode="cover"
          />

          <BlockView
            inset={'top'}
            padding={{ horizontal: 16 }}
            zIndex={10}
          >
            <TouchableOpacity
              width={32}
              height={32}
              center
              radius={BorderRadius.RADIUS_08}
              backgroundColor={ColorsV2.neutral50}
              onPress={onPressBack}
            >
              <IconImage
                source={IconAssets.icBack}
                size={24}
              />
            </TouchableOpacity>
          </BlockView>
        </Animated.View>
        <Animated.ScrollView
          showsVerticalScrollIndicator={false}
          style={[styles.scrollView, animatedContainerStyle]}
          onScroll={onScroll}
        >
          {children}
        </Animated.ScrollView>
        {footer}
      </BlockView>
    );
  },
);
