import React, {
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Animated,
  ColorValue,
  EmitterSubscription,
  Keyboard,
  KeyboardEventName,
  StyleSheet,
  ViewProps,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useIsFocused } from '@react-navigation/native';

import { AnimationHelpers, DeviceHelper } from '../../helpers';
import { Maybe } from '../../types';
import { BlockView } from '../block-view';
import { BlockViewProps } from '../block-view/types';
import { ConditionView } from '../condition-view';
import { SizedBox } from '../sized-box';
import { styles } from './styles';

interface ViewBottomKeyboardProps {
  children: ReactNode;
  backgroundColor: string;
  viewOnBottom?: ReactNode;
  bottomView?: ReactNode;
  paddingBottom?: number;
  bottomViewStyle?: ViewProps['style'];
  bottomViewBackgroundColor?: ColorValue;
}

export const ViewBottomAvoidKeyboard = ({
  children,
  backgroundColor,
  bottomView,
  paddingBottom,
  bottomViewStyle,
  bottomViewBackgroundColor,
  viewOnBottom,
}: ViewBottomKeyboardProps) => {
  const inset = useSafeAreaInsets();

  const isFocused = useIsFocused();

  const [heightViewOnBottom, setHeightViewOnBottom] = useState(0);
  const [heightBottomView, setHeightBottomView] = useState(0);
  const [heightKeyboard, setHeightKeyboard] = useState(0);

  const keyboardShowListener = useRef<Maybe<EmitterSubscription>>(null);
  const keyboardDidShowListener = useRef<Maybe<EmitterSubscription>>(null);
  const keyboardHideListener = useRef<Maybe<EmitterSubscription>>(null);

  const animationValue = useRef(new Animated.Value(0)).current;
  const keyboardHeightValue = useRef(new Animated.Value(0)).current;

  const spacingBottomDefault = useMemo(() => {
    return paddingBottom || inset.bottom;
  }, [inset.bottom, paddingBottom]);

  // Nếu key board show thì sẽ có heightKeyboard
  const isKeyboardShow = useMemo(() => {
    return heightKeyboard > 0;
  }, [heightKeyboard]);

  const fakeHeight = useMemo(() => {
    // Nếu key board show thì heightKeyboard + heightBottomView -> vì khi keyboard show chỉ đẩy BottomView lên nên phải cộng bottom view
    // Vì android set windowSoftInputMode="adjustResize nên đã auto đẩy view nên chỉ cần bằng heightBottomView
    const heightWhenKeyboardShow = DeviceHelper.isIos
      ? heightKeyboard + heightBottomView
      : heightBottomView;

    // Nếu key board hide thì heightKeyboard + heightViewOnBottom -> vì khi keyboard hide thì phải đẩy BottomView xuống nên phải hêt các view ở bottom heightViewOnBottom
    const heightWhenKeyboardHide = heightKeyboard + heightViewOnBottom;
    const result = isKeyboardShow
      ? heightWhenKeyboardShow
      : heightWhenKeyboardHide;

    return result;
  }, [heightBottomView, heightKeyboard, heightViewOnBottom, isKeyboardShow]);

  const translateY = Animated.multiply(
    animationValue,
    keyboardHeightValue,
  ).interpolate({
    inputRange: [0, 1],
    outputRange: [0, -1], // đảo dấu để translateY lên trên
  });

  const onLayoutBottomView = useCallback<
    NonNullable<BlockViewProps['onLayout']>
  >((e) => {
    AnimationHelpers.runLayoutAnimation();
    setHeightBottomView(Math.ceil(e.nativeEvent.layout.height));
  }, []);

  const onLayoutViewOnBottom = useCallback<
    NonNullable<BlockViewProps['onLayout']>
  >((e) => {
    AnimationHelpers.runLayoutAnimation();
    setHeightViewOnBottom(Math.ceil(e.nativeEvent.layout.height));
  }, []);

  const removeListener = useCallback(() => {
    keyboardShowListener.current && keyboardShowListener.current?.remove();
    keyboardHideListener.current && keyboardHideListener.current?.remove();
  }, []);

  useEffect(() => {
    if (!isFocused) {
      removeListener();
      return;
    }

    keyboardShowListener.current = Keyboard.addListener(
      'keyboardWillShow',
      (e) => {
        keyboardHeightValue.setValue(
          Math.ceil(e.endCoordinates.height) - spacingBottomDefault,
        );
        Animated.timing(animationValue, {
          toValue: 1,
          duration: e.duration,
          useNativeDriver: true,
        }).start();
      },
    );

    keyboardDidShowListener.current = Keyboard.addListener(
      'keyboardDidShow',
      (e) => {
        setHeightKeyboard(
          Math.ceil(e.endCoordinates.height) - spacingBottomDefault,
        );
      },
    );

    const eventKeyboardHide: KeyboardEventName = DeviceHelper.isIos
      ? 'keyboardWillHide'
      : 'keyboardDidHide';
    keyboardHideListener.current = Keyboard.addListener(
      eventKeyboardHide,
      (e) => {
        setHeightKeyboard(0);
        Animated.timing(animationValue, {
          toValue: 0,
          duration: e.duration,
          useNativeDriver: true,
        }).start();
      },
    );

    return removeListener;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <BlockView
      flex
      backgroundColor={backgroundColor}
    >
      {children}
      <SizedBox height={fakeHeight} />

      {/* /Khi keyboard show sẽ đè lên view này nên ẩn để tránh lỗi bên android */}
      <ConditionView
        condition={!isKeyboardShow}
        viewTrue={
          <BlockView
            style={styles.viewOnBottom}
            onLayout={onLayoutViewOnBottom}
          >
            {viewOnBottom}
            <SizedBox height={heightBottomView} />
          </BlockView>
        }
      />
      <Animated.View
        style={StyleSheet.flatten([
          {
            transform: [{ translateY }],
            paddingBottom: spacingBottomDefault,
            backgroundColor: bottomViewBackgroundColor || backgroundColor,
            ...styles.bottomContainer,
          },
          bottomViewStyle,
        ])}
        onLayout={onLayoutBottomView}
      >
        {bottomView}
      </Animated.View>
    </BlockView>
  );
};
