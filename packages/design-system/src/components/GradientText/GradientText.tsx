import React from 'react';
import { StyleProp, TextStyle } from 'react-native';
import LinearGradient from 'react-native-linear-gradient';
import MaskedView from '@react-native-masked-view/masked-view';

import { CText } from '../text';
import { CommonTextProps } from '../text/types';

export interface GradientTextProps extends CommonTextProps {
  /**
   * Array of colors for the gradient effect
   */
  colors: string[];
  /**
   * Starting point of the gradient (x, y coordinates from 0 to 1)
   */
  start?: { x: number; y: number };
  /**
   * Ending point of the gradient (x, y coordinates from 0 to 1)
   */
  end?: { x: number; y: number };
  /**
   * Text style to apply to the gradient text
   */
  style?: StyleProp<TextStyle>;
  /**
   * Maximum number of lines to display
   */
  numberOfLines?: number;
  /**
   * Text content to display with gradient effect
   */
  children: React.ReactNode;
}

/**
 * Renders text with a gradient color effect using LinearGradient and MaskedView
 * Purpose: Provides visually appealing gradient text for enhanced UI design
 * @param colors - Array of gradient colors (required)
 * @param start - Gradient start coordinates (optional, defaults to LinearGradient default)
 * @param end - Gradient end coordinates (optional, defaults to LinearGradient default)
 * @param style - Text styling options (optional)
 * @param numberOfLines - Text line limit (optional)
 * @param children - Text content to render (required)
 * @returns {JSX.Element} Text component with gradient color effect
 */
export const GradientText: React.FC<GradientTextProps> = ({
  start,
  end,
  colors,
  children,
  ...props
}) => {
  return (
    <MaskedView maskElement={<CText {...props}>{children}</CText>}>
      <LinearGradient
        colors={colors}
        start={start}
        end={end}
      >
        <CText
          opacity={0}
          {...props}
        >
          {children}
        </CText>
      </LinearGradient>
    </MaskedView>
  );
};
