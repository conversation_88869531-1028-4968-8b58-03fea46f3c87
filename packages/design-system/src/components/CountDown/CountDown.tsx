import React, { memo, useEffect, useRef, useState } from 'react';
import {
  AppState,
  AppStateStatus,
  GestureResponderEvent,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import { sprintf } from 'sprintf-js';

import { <PERSON><PERSON><PERSON>elper } from '../../helpers';
import { ColorsV2, FontFamily, FontSizes } from '../../tokens';

const DEFAULT_DIGIT_STYLE: ViewStyle = {
  backgroundColor: ColorsV2.neutralWhite,
  borderRadius: 6,
  width: 28 * DeviceHelper.WIDTH_RATIO,
  height: 28 * DeviceHelper.WIDTH_RATIO,
  justifyContent: 'center',
  alignItems: 'center',
};
const DEFAULT_DIGIT_TXT_STYLE: TextStyle = {
  fontSize: FontSizes.SIZE_12,
  fontFamily: FontFamily.bold,
  color: ColorsV2.green500,
};
const DEFAULT_TIME_LABEL_STYLE: TextStyle = { color: ColorsV2.neutral800 };
const DEFAULT_SEPARATOR_STYLE: TextStyle = {
  color: ColorsV2.neutralWhite,
  width: 0,
};
const DEFAULT_TIME_TO_SHOW = ['D', 'H', 'M', 'S'] as const;
const DEFAULT_TIME_LABELS = {
  d: 'Days',
  h: 'Hours',
  m: 'Minutes',
  s: 'Seconds',
};

interface TimeLabels {
  d?: string;
  h?: string;
  m?: string;
  s?: string;
}

type TimeLabelKeys = keyof typeof DEFAULT_TIME_LABELS;

export interface CountDownProps {
  id?: string;
  digitStyle?: ViewStyle;
  digitTxtStyle?: TextStyle;
  timeLabelStyle?: TextStyle;
  separatorStyle?: TextStyle;
  timeToShow?: Array<Uppercase<TimeLabelKeys>>;
  timeLabels?: TimeLabels;
  showSeparator?: boolean;
  size?: number;
  until?: number;
  running?: boolean;
  style?: ViewStyle;
  onChange?: (timeLeft: number) => void;
  onPress?: (event: GestureResponderEvent) => void;
  onFinish?: () => void;
}

export const CountDown: React.FC<CountDownProps> = memo(
  ({
    id,
    digitStyle = DEFAULT_DIGIT_STYLE,
    digitTxtStyle = DEFAULT_DIGIT_TXT_STYLE,
    timeLabelStyle = DEFAULT_TIME_LABEL_STYLE,
    separatorStyle = DEFAULT_SEPARATOR_STYLE,
    timeToShow = DEFAULT_TIME_TO_SHOW,
    timeLabels = DEFAULT_TIME_LABELS,
    showSeparator = false,
    size = 15,
    until = 0,
    running = true,
    style,
    onChange,
    onPress,
    onFinish,
  }) => {
    const [timeLeft, setTimeLeft] = useState(Math.max(until, 0));
    const [wentBackgroundAt, setWentBackgroundAt] = useState<number | null>(
      null,
    );

    const intervalRef = useRef<number | null>(null);

    useEffect(() => {
      intervalRef.current = setInterval(updateTimer, 1000);
      const subscription = AppState.addEventListener(
        'change',
        handleAppStateChange,
      );
      return () => {
        clearInterval(intervalRef.current!);
        subscription.remove();
      };
    }, []);

    useEffect(() => {
      setTimeLeft(Math.max(until, 0));
    }, [until, id]);

    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && wentBackgroundAt && running) {
        const diff = (Date.now() - wentBackgroundAt) / 1000.0;
        setTimeLeft((prev) => Math.max(0, prev - diff));
      }
      if (nextAppState === 'background') {
        setWentBackgroundAt(Date.now());
      }
    };

    const getTimeLeft = () => {
      return {
        seconds: Math.floor(timeLeft % 60),
        minutes: Math.floor(timeLeft / 60) % 60,
        hours: Math.floor(timeLeft / 3600) % 24,
        days: Math.floor(timeLeft / 86400),
      };
    };

    const updateTimer = () => {
      if (!running) return;

      setTimeLeft((prev) => {
        const next = Math.max(0, prev - 1);

        // Gọi onFinish khi countdown kết thúc
        if (prev > 0 && next === 0) {
          onFinish?.();
        }

        // Gọi onChange mỗi giây
        onChange?.(next);

        return next;
      });
    };

    const renderDigit = (d: string) => (
      <View
        style={[
          styles.digitCont,
          { width: size * 2.3, height: size * 2.6 },
          digitStyle,
        ]}
      >
        <Text style={[styles.digitTxt, { fontSize: size }, digitTxtStyle]}>
          {d}
        </Text>
      </View>
    );

    const renderLabel = (label?: string) =>
      label ? (
        <Text
          style={[styles.timeTxt, { fontSize: size / 1.8 }, timeLabelStyle]}
        >
          {label}
        </Text>
      ) : null;

    const renderDoubleDigits = (label: string | undefined, digits: string) => (
      <View style={styles.doubleDigitCont}>
        <View style={styles.timeInnerCont}>{renderDigit(digits)}</View>
        {renderLabel(label)}
      </View>
    );

    const renderSeparator = () => (
      <View style={{ justifyContent: 'center', alignItems: 'center' }}>
        <Text
          style={[
            styles.separatorTxt,
            { fontSize: size * 1.2 },
            separatorStyle,
          ]}
        >
          :
        </Text>
      </View>
    );

    const { days, hours, minutes, seconds } = getTimeLeft();
    const newTime = sprintf(
      '%02d:%02d:%02d:%02d',
      days,
      hours,
      minutes,
      seconds,
    ).split(':');
    const Wrapper = onPress ? TouchableOpacity : View;

    return (
      <View style={style}>
        <Wrapper
          style={styles.timeCont}
          onPress={onPress}
        >
          {timeToShow.includes('D') &&
            renderDoubleDigits(timeLabels.d, newTime[0])}
          {showSeparator &&
            timeToShow.includes('D') &&
            timeToShow.includes('H') &&
            renderSeparator()}
          {timeToShow.includes('H') &&
            renderDoubleDigits(timeLabels.h, newTime[1])}
          {showSeparator &&
            timeToShow.includes('H') &&
            timeToShow.includes('M') &&
            renderSeparator()}
          {timeToShow.includes('M') &&
            renderDoubleDigits(timeLabels.m, newTime[2])}
          {showSeparator &&
            timeToShow.includes('M') &&
            timeToShow.includes('S') &&
            renderSeparator()}
          {timeToShow.includes('S') &&
            renderDoubleDigits(timeLabels.s, newTime[3])}
        </Wrapper>
      </View>
    );
  },
);

const styles = StyleSheet.create({
  timeCont: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  timeTxt: {
    color: 'white',
    marginVertical: 2,
    backgroundColor: 'transparent',
  },
  timeInnerCont: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  digitCont: {
    borderRadius: 5,
    marginHorizontal: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  doubleDigitCont: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  digitTxt: {
    color: 'white',
    fontWeight: 'bold',
    fontVariant: ['tabular-nums'],
  },
  separatorTxt: {
    backgroundColor: 'transparent',
    fontWeight: 'bold',
  },
});
