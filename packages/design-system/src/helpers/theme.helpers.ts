export class ThemeHelpers {
  static hexWithOpacity(hex: string, opacity: number): string {
    if (!hex) {
      return '';
    }

    // Bỏ ký tự # nếu có
    let cleanHex = hex.replace('#', '');

    // Nếu là mã 3 ký tự rút gọn, mở rộng nó ra (ví dụ: f00 => ff0000)
    if (cleanHex.length === 3) {
      cleanHex = cleanHex
        .split('')
        .map((c) => c + c)
        .join('');
    }

    // Chuyển opacity (0-1) thành giá trị hex 2 ký tự
    const alpha = Math.round(opacity * 255)
      .toString(16)
      .padStart(2, '0');

    return `#${cleanHex}${alpha}`;
  }
}
