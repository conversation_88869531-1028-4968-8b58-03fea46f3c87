import { IObjectText } from './global';

export type RewardItem = {
  _id: string;
  brandText: IObjectText;
  from: 'SYSTEM' | 'SYSTEM_WITH_PARTNER' | string;
  image?: string;
  isShowRedeem?: boolean;
  title: IObjectText;
};

export enum RewardGroupType {
  RECOMMEND_FOR_YOU = 'RECOMMEND_FOR_YOU',
  EXCLUSIVE_DEAL = 'EXCLUSIVE_DEAL',
  TOP_DEAL = 'TOP_DEAL',
  FLASH_SALE = 'FLASH_SALE',
}
export type RewardGroup = {
  Weight: number;
  rewards: RewardItem[];
  text: IObjectText;
  type: RewardGroupType;
  endDate?: string;
};
export interface BrandInfo {
  image: string;
  name: string;
  text: IObjectText;
}

export type PromotionItem = {
  _id: string;
  brandInfo: BrandInfo;
  content?: IObjectText;
  createdAt: string;
  expired: string;
  image?: string;
  note?: IObjectText;
  promotionCode: string;
  source?: string;
  title: IObjectText;
  type: 'GIFT' | 'MARKETING_CAMPAIGN' | string;
};

interface SocialLinks {
  facebook?: string;
  instagram?: string;
  website?: string;
  hotline?: string;
}

export interface RewardDetailItem {
  _id: string;
  brandInfo: BrandInfo;
  content: IObjectText;
  endDate: string;
  from: 'SYSTEM_WITH_PARTNER' | string;
  image: string;
  isoCode: string;
  note: IObjectText;
  point: number;
  social: SocialLinks;
  startDate: string;
  title: IObjectText;
}
