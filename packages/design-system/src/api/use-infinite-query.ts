import { useEffect, useRef } from 'react';
import {
  InfiniteData,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
} from '@tanstack/react-query';

import { useAppStore } from '../stores';
import { IApiError } from '../types';
import { apiRequest, createCancelToken } from './api-request';
import { EndpointKeys } from './endpoints';
import { ApiType } from './types';

/**
 * Custom hook for infinite API queries using react-query
 * @param key - API endpoint key
 * @param params - Request parameters
 * @param queryKey - Query key for caching
 * @param options - Additional infinite query options
 */
export function useApiInfiniteQuery<T extends EndpointKeys>({
  key,
  options,
  params,
  queryKey,
}: {
  key: T;
  queryKey?: unknown[];
  params?: ApiType[T]['params'];
  options?: Omit<
    UseInfiniteQueryOptions<
      ApiType[T]['response'],
      IApiError,
      ApiType[T]['response'],
      ApiType[T]['response'],
      unknown[]
    >,
    'queryFn' | 'queryKey' | 'initialPageParam'
  >;
}) {
  // Create a ref for the cancel token to persist across renders
  const { isoCode } = useAppStore();

  const cancelTokenRef = useRef(createCancelToken());

  // Reset cancel token on unmount
  useEffect(() => {
    return () => {
      cancelTokenRef.current.cancel('Component unmounted');
    };
  }, []);

  return useInfiniteQuery({
    queryKey: [key, isoCode, JSON.stringify(params ?? {}), ...(queryKey || [])],
    queryFn: ({ pageParam = 1 }) => {
      // Reset cancel token for new request
      cancelTokenRef.current = createCancelToken();

      return apiRequest({
        key,
        params: {
          ...params,
          page: pageParam,
        } as ApiType[T]['params'],
        cancelToken: cancelTokenRef.current,
      });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      // Default pagination logic - can be overridden in options
      if (Array.isArray(lastPage) && lastPage.length === 0) {
        return undefined; // No more pages
      }
      return allPages.length + 1;
    },
    ...options,
  }) as UseInfiniteQueryResult<InfiniteData<ApiType[T]['response']>, IApiError>;
}
